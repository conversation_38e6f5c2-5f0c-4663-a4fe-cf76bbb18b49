# -*- coding: utf-8 -*-
"""
مسارات لوحة التحكم
Dashboard Routes
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, desc

from app import db
from app.models import Camera, Recording, Alert, User

bp = Blueprint('dashboard', __name__)

@bp.route('/')
@login_required
def index():
    """لوحة التحكم الرئيسية"""
    
    # إحصائيات الكاميرات
    total_cameras = Camera.query.count()
    online_cameras = Camera.query.filter_by(connection_status='connected').count()
    recording_cameras = Camera.query.filter_by(is_recording=True).count()
    
    # إحصائيات التسجيلات
    total_recordings = Recording.query.count()
    today_recordings = Recording.query.filter(
        Recording.created_at >= datetime.utcnow().date()
    ).count()
    
    # إحصائيات التنبيهات
    unread_alerts = Alert.query.filter_by(is_read=False).count()
    today_alerts = Alert.query.filter(
        Alert.triggered_at >= datetime.utcnow().date()
    ).count()
    
    # الكاميرات الحديثة
    recent_cameras = Camera.query.order_by(desc(Camera.created_at)).limit(5).all()
    
    # التسجيلات الحديثة
    recent_recordings = Recording.query.order_by(desc(Recording.created_at)).limit(5).all()
    
    # التنبيهات الحديثة
    recent_alerts = Alert.query.order_by(desc(Alert.triggered_at)).limit(5).all()
    
    # إحصائيات التخزين
    total_storage = db.session.query(func.sum(Recording.file_size)).scalar() or 0
    
    stats = {
        'cameras': {
            'total': total_cameras,
            'online': online_cameras,
            'recording': recording_cameras,
            'offline': total_cameras - online_cameras
        },
        'recordings': {
            'total': total_recordings,
            'today': today_recordings,
            'storage_mb': round(total_storage / (1024 * 1024), 2)
        },
        'alerts': {
            'unread': unread_alerts,
            'today': today_alerts
        }
    }
    
    return render_template('dashboard/index.html',
                         stats=stats,
                         recent_cameras=recent_cameras,
                         recent_recordings=recent_recordings,
                         recent_alerts=recent_alerts)

@bp.route('/api/stats')
@login_required
def api_stats():
    """API لإحصائيات لوحة التحكم"""
    
    # إحصائيات الكاميرات
    total_cameras = Camera.query.count()
    online_cameras = Camera.query.filter_by(connection_status='connected').count()
    recording_cameras = Camera.query.filter_by(is_recording=True).count()
    
    # إحصائيات التسجيلات
    total_recordings = Recording.query.count()
    today_recordings = Recording.query.filter(
        Recording.created_at >= datetime.utcnow().date()
    ).count()
    
    # إحصائيات التنبيهات
    unread_alerts = Alert.query.filter_by(is_read=False).count()
    
    return jsonify({
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'recording_cameras': recording_cameras,
        'total_recordings': total_recordings,
        'today_recordings': today_recordings,
        'unread_alerts': unread_alerts
    })

@bp.route('/alerts')
@login_required
def alerts():
    """صفحة التنبيهات"""
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # فلترة التنبيهات
    alert_type = request.args.get('type')
    severity = request.args.get('severity')
    status = request.args.get('status')
    
    query = Alert.query
    
    if alert_type:
        query = query.filter_by(alert_type=alert_type)
    
    if severity:
        query = query.filter_by(severity=severity)
    
    if status:
        query = query.filter_by(status=status)
    
    # ترتيب حسب التاريخ
    alerts_pagination = query.order_by(desc(Alert.triggered_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات التنبيهات
    alert_stats = {
        'total': Alert.query.count(),
        'unread': Alert.query.filter_by(is_read=False).count(),
        'today': Alert.query.filter(
            Alert.triggered_at >= datetime.utcnow().date()
        ).count(),
        'by_type': dict(
            db.session.query(Alert.alert_type, func.count(Alert.id))
            .group_by(Alert.alert_type).all()
        ),
        'by_severity': dict(
            db.session.query(Alert.severity, func.count(Alert.id))
            .group_by(Alert.severity).all()
        )
    }
    
    return render_template('dashboard/alerts.html',
                         alerts=alerts_pagination.items,
                         pagination=alerts_pagination,
                         stats=alert_stats,
                         current_filters={
                             'type': alert_type,
                             'severity': severity,
                             'status': status
                         })

@bp.route('/alerts/<int:alert_id>/acknowledge', methods=['POST'])
@login_required
def acknowledge_alert(alert_id):
    """الإقرار بالتنبيه"""
    
    alert = Alert.query.get_or_404(alert_id)
    alert.acknowledge(current_user.id)
    
    return jsonify({
        'success': True,
        'message': 'تم الإقرار بالتنبيه'
    })

@bp.route('/alerts/<int:alert_id>/resolve', methods=['POST'])
@login_required
def resolve_alert(alert_id):
    """حل التنبيه"""
    
    alert = Alert.query.get_or_404(alert_id)
    action = request.json.get('action', '')
    
    alert.resolve(current_user.id, action)
    
    return jsonify({
        'success': True,
        'message': 'تم حل التنبيه'
    })

@bp.route('/alerts/<int:alert_id>/dismiss', methods=['POST'])
@login_required
def dismiss_alert(alert_id):
    """تجاهل التنبيه"""
    
    alert = Alert.query.get_or_404(alert_id)
    alert.dismiss(current_user.id)
    
    return jsonify({
        'success': True,
        'message': 'تم تجاهل التنبيه'
    })

@bp.route('/system-info')
@login_required
def system_info():
    """معلومات النظام"""
    
    import psutil
    import platform
    from datetime import datetime
    
    # معلومات النظام
    system_info = {
        'platform': platform.system(),
        'platform_release': platform.release(),
        'platform_version': platform.version(),
        'architecture': platform.machine(),
        'hostname': platform.node(),
        'processor': platform.processor(),
        'python_version': platform.python_version()
    }
    
    # معلومات الذاكرة
    memory = psutil.virtual_memory()
    memory_info = {
        'total': round(memory.total / (1024**3), 2),
        'available': round(memory.available / (1024**3), 2),
        'used': round(memory.used / (1024**3), 2),
        'percentage': memory.percent
    }
    
    # معلومات القرص الصلب
    disk = psutil.disk_usage('/')
    disk_info = {
        'total': round(disk.total / (1024**3), 2),
        'used': round(disk.used / (1024**3), 2),
        'free': round(disk.free / (1024**3), 2),
        'percentage': round((disk.used / disk.total) * 100, 2)
    }
    
    # معلومات المعالج
    cpu_info = {
        'physical_cores': psutil.cpu_count(logical=False),
        'total_cores': psutil.cpu_count(logical=True),
        'max_frequency': round(psutil.cpu_freq().max, 2) if psutil.cpu_freq() else 'غير متاح',
        'current_frequency': round(psutil.cpu_freq().current, 2) if psutil.cpu_freq() else 'غير متاح',
        'usage_percentage': psutil.cpu_percent(interval=1)
    }
    
    return render_template('dashboard/system_info.html',
                         system_info=system_info,
                         memory_info=memory_info,
                         disk_info=disk_info,
                         cpu_info=cpu_info)

@bp.route('/api/system/status')
@login_required
def api_system_status():
    """API لحالة النظام"""
    
    try:
        # فحص قاعدة البيانات
        db.session.execute('SELECT 1')
        db_status = 'online'
    except:
        db_status = 'offline'
    
    # فحص الكاميرات
    total_cameras = Camera.query.count()
    online_cameras = Camera.query.filter_by(connection_status='connected').count()
    
    if total_cameras == 0:
        camera_status = 'no_cameras'
    elif online_cameras == total_cameras:
        camera_status = 'all_online'
    elif online_cameras > 0:
        camera_status = 'partial_online'
    else:
        camera_status = 'all_offline'
    
    # تحديد الحالة العامة
    if db_status == 'offline':
        overall_status = 'offline'
    elif camera_status in ['all_offline', 'no_cameras']:
        overall_status = 'warning'
    else:
        overall_status = 'online'
    
    return jsonify({
        'status': overall_status,
        'database': db_status,
        'cameras': {
            'status': camera_status,
            'total': total_cameras,
            'online': online_cameras
        },
        'timestamp': datetime.utcnow().isoformat()
    })
