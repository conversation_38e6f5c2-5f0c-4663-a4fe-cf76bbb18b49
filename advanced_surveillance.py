#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات المتقدم - مع جميع المميزات
Advanced Surveillance System - With All Features
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
import sqlite3
import hashlib
import os
import cv2
import threading
import time
from datetime import datetime, timedelta
import json

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'surveillance-advanced-key-2024'

# إعداد قاعدة البيانات
DATABASE = 'surveillance_advanced.db'

def init_db():
    """تهيئة قاعدة البيانات المتقدمة"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    # التحقق من وجود عمود القناة وإضافته إذا لم يكن موجوداً
    cursor.execute("PRAGMA table_info(cameras)")
    columns = [column[1] for column in cursor.fetchall()]
    if 'channel' not in columns:
        cursor.execute('ALTER TABLE cameras ADD COLUMN channel INTEGER DEFAULT 1')
        print("✅ تم إضافة عمود القناة لجدول الكاميرات")
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            full_name TEXT,
            email TEXT,
            is_admin INTEGER DEFAULT 0,
            can_view_cameras INTEGER DEFAULT 1,
            can_control_cameras INTEGER DEFAULT 0,
            can_manage_recordings INTEGER DEFAULT 0,
            can_manage_users INTEGER DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')
    
    # جدول الكاميرات المتقدم
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cameras (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            ip_address TEXT NOT NULL,
            port INTEGER DEFAULT 554,
            channel INTEGER DEFAULT 1,
            username TEXT,
            password TEXT,
            camera_type TEXT DEFAULT 'IP',
            protocol TEXT DEFAULT 'RTSP',
            brand TEXT,
            model TEXT,
            location TEXT,
            status TEXT DEFAULT 'offline',
            is_recording INTEGER DEFAULT 0,
            motion_detection INTEGER DEFAULT 0,
            recording_quality TEXT DEFAULT 'medium',
            stream_url TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_seen TIMESTAMP
        )
    ''')
    
    # جدول التسجيلات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS recordings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            camera_id INTEGER,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER,
            duration INTEGER,
            recording_type TEXT DEFAULT 'manual',
            quality TEXT DEFAULT 'medium',
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP,
            status TEXT DEFAULT 'completed',
            is_protected INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (camera_id) REFERENCES cameras (id)
        )
    ''')
    
    # جدول التنبيهات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            camera_id INTEGER,
            title TEXT NOT NULL,
            message TEXT,
            alert_type TEXT NOT NULL,
            severity TEXT DEFAULT 'medium',
            status TEXT DEFAULT 'new',
            is_read INTEGER DEFAULT 0,
            triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            acknowledged_at TIMESTAMP,
            FOREIGN KEY (camera_id) REFERENCES cameras (id)
        )
    ''')
    
    # جدول الصور المحفوظة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS snapshots (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            camera_id INTEGER,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            snapshot_type TEXT DEFAULT 'manual',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (camera_id) REFERENCES cameras (id)
        )
    ''')
    
    # إنشاء المستخدم الافتراضي
    password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password, full_name, email, is_admin, can_view_cameras, can_control_cameras, can_manage_recordings, can_manage_users) 
        VALUES (?, ?, ?, ?, 1, 1, 1, 1, 1)
    ''', ('admin', password_hash, 'مدير النظام', '<EMAIL>'))
    
    # إضافة كاميرات تجريبية متنوعة مع القنوات
    cameras_data = [
        ('كاميرا المدخل الرئيسي', 'كاميرا مراقبة المدخل الرئيسي للمبنى', '*************', 554, 1, 'admin', 'admin123', 'IP', 'RTSP', 'Hikvision', 'DS-2CD2043G0-I', 'المدخل الرئيسي', 'online', 1, 1),
        ('كاميرا الحديقة', 'مراقبة الحديقة الخلفية', '*************', 554, 1, 'admin', 'admin123', 'IP', 'RTSP', 'Dahua', 'IPC-HFW4431R-Z', 'الحديقة الخلفية', 'offline', 0, 1),
        ('كاميرا المكتب', 'مراقبة المكتب الإداري', '*************', 80, 1, 'user', 'pass', 'IP', 'HTTP', 'Uniview', 'IPC322LR3-VSPF28-D', 'المكتب الإداري', 'online', 0, 0),
        ('كاميرا الموقف', 'مراقبة موقف السيارات', '*************', 554, 1, 'admin', 'admin123', 'IP', 'RTSP', 'Axis', 'M3046-V', 'موقف السيارات', 'connecting', 1, 1),
        ('DVR القاعة الكبرى - قناة 1', 'نظام DVR للقاعة الكبرى - القناة الأولى', '*************', 37777, 1, 'admin', '123456', 'DVR', 'TCP', 'Dahua', 'XVR5108HS-4KL-X', 'القاعة الكبرى', 'online', 1, 0),
        ('DVR القاعة الكبرى - قناة 2', 'نظام DVR للقاعة الكبرى - القناة الثانية', '*************', 37777, 2, 'admin', '123456', 'DVR', 'TCP', 'Dahua', 'XVR5108HS-4KL-X', 'القاعة الكبرى', 'online', 0, 1),
        ('NVR المخزن - قناة 1', 'نظام NVR لمراقبة المخزن - القناة الأولى', '*************', 554, 1, 'admin', 'admin123', 'NVR', 'RTSP', 'Dahua', 'NVR4108HS-8P-4KS2', 'المخزن', 'online', 1, 1),
        ('NVR المخزن - قناة 4', 'نظام NVR لمراقبة المخزن - القناة الرابعة', '*************', 554, 4, 'admin', 'admin123', 'NVR', 'RTSP', 'Dahua', 'NVR4108HS-8P-4KS2', 'المخزن', 'connecting', 0, 0)
    ]

    for camera in cameras_data:
        cursor.execute('''
            INSERT OR IGNORE INTO cameras
            (name, description, ip_address, port, channel, username, password, camera_type, protocol, brand, model, location, status, is_recording, motion_detection)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', camera)
    
    # إضافة تسجيلات تجريبية
    recordings_data = [
        (1, 'recording_20241201_080000.mp4', '/recordings/camera1/recording_20241201_080000.mp4', 1024*1024*50, 3600, 'scheduled', 'high', '2024-12-01 08:00:00', '2024-12-01 09:00:00', 'completed', 0),
        (1, 'motion_20241201_143000.mp4', '/recordings/camera1/motion_20241201_143000.mp4', 1024*1024*25, 1800, 'motion', 'medium', '2024-12-01 14:30:00', '2024-12-01 15:00:00', 'completed', 1),
        (3, 'manual_20241201_120000.mp4', '/recordings/camera3/manual_20241201_120000.mp4', 1024*1024*75, 2700, 'manual', 'high', '2024-12-01 12:00:00', '2024-12-01 12:45:00', 'completed', 0),
        (5, 'scheduled_20241201_200000.mp4', '/recordings/camera5/scheduled_20241201_200000.mp4', 1024*1024*100, 7200, 'scheduled', 'medium', '2024-12-01 20:00:00', '2024-12-01 22:00:00', 'completed', 0)
    ]
    
    for recording in recordings_data:
        cursor.execute('''
            INSERT OR IGNORE INTO recordings 
            (camera_id, filename, file_path, file_size, duration, recording_type, quality, start_time, end_time, status, is_protected) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', recording)
    
    # إضافة تنبيهات تجريبية
    alerts_data = [
        (1, 'كشف حركة - كاميرا المدخل', 'تم اكتشاف حركة غير عادية في المدخل الرئيسي', 'motion', 'high', 'new', 0, '2024-12-01 14:30:15'),
        (2, 'انقطاع الاتصال - كاميرا الحديقة', 'انقطع الاتصال مع كاميرا الحديقة', 'connection', 'medium', 'acknowledged', 1, '2024-12-01 13:45:22'),
        (4, 'امتلاء مساحة التخزين', 'مساحة التخزين وصلت إلى 85%', 'system', 'warning', 'new', 0, '2024-12-01 16:20:10'),
        (1, 'بدء التسجيل المجدول', 'تم بدء التسجيل المجدول للكاميرا', 'recording', 'info', 'resolved', 1, '2024-12-01 08:00:00'),
        (5, 'كشف حركة متعددة', 'تم اكتشاف عدة حركات في القاعة الكبرى', 'motion', 'critical', 'new', 0, '2024-12-01 17:15:30')
    ]
    
    for alert in alerts_data:
        cursor.execute('''
            INSERT OR IGNORE INTO alerts 
            (camera_id, title, message, alert_type, severity, status, is_read, triggered_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', alert)
    
    conn.commit()
    conn.close()

def get_db():
    """الحصول على اتصال قاعدة البيانات"""
    return sqlite3.connect(DATABASE)

def check_login(username, password):
    """التحقق من بيانات تسجيل الدخول"""
    conn = get_db()
    cursor = conn.cursor()
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    cursor.execute('''
        SELECT id, username, full_name, is_admin, can_view_cameras, can_control_cameras, can_manage_recordings, can_manage_users 
        FROM users WHERE username = ? AND password = ? AND is_active = 1
    ''', (username, password_hash))
    
    user = cursor.fetchone()
    
    if user:
        # تحديث آخر تسجيل دخول
        cursor.execute('UPDATE users SET last_login = ? WHERE id = ?', (datetime.now(), user[0]))
        conn.commit()
    
    conn.close()
    return user

def get_cameras():
    """الحصول على قائمة الكاميرات مع التفاصيل"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, name, description, ip_address, port, channel, camera_type, protocol, brand, model, location,
               status, is_recording, motion_detection, recording_quality, created_at, last_seen
        FROM cameras ORDER BY created_at DESC
    ''')
    cameras = cursor.fetchall()
    conn.close()
    return cameras

def get_recordings():
    """الحصول على قائمة التسجيلات"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT r.id, r.filename, r.file_size, r.duration, r.recording_type, r.quality, 
               r.start_time, r.end_time, r.status, r.is_protected, c.name as camera_name
        FROM recordings r
        JOIN cameras c ON r.camera_id = c.id
        ORDER BY r.start_time DESC
        LIMIT 20
    ''')
    recordings = cursor.fetchall()
    conn.close()
    return recordings

def get_alerts():
    """الحصول على قائمة التنبيهات"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT a.id, a.title, a.message, a.alert_type, a.severity, a.status, 
               a.is_read, a.triggered_at, c.name as camera_name
        FROM alerts a
        LEFT JOIN cameras c ON a.camera_id = c.id
        ORDER BY a.triggered_at DESC
        LIMIT 20
    ''')
    alerts = cursor.fetchall()
    conn.close()
    return alerts

def get_stats():
    """الحصول على إحصائيات النظام المتقدمة"""
    conn = get_db()
    cursor = conn.cursor()
    
    # إحصائيات الكاميرات
    cursor.execute('SELECT COUNT(*) FROM cameras')
    total_cameras = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM cameras WHERE status = 'online'")
    online_cameras = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM cameras WHERE is_recording = 1")
    recording_cameras = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM cameras WHERE motion_detection = 1")
    motion_cameras = cursor.fetchone()[0]
    
    # إحصائيات التسجيلات
    cursor.execute('SELECT COUNT(*) FROM recordings')
    total_recordings = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM recordings WHERE DATE(start_time) = DATE("now")')
    today_recordings = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(file_size) FROM recordings')
    total_storage = cursor.fetchone()[0] or 0
    
    cursor.execute('SELECT SUM(duration) FROM recordings')
    total_duration = cursor.fetchone()[0] or 0
    
    # إحصائيات التنبيهات
    cursor.execute('SELECT COUNT(*) FROM alerts WHERE is_read = 0')
    unread_alerts = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM alerts WHERE DATE(triggered_at) = DATE("now")')
    today_alerts = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM alerts WHERE severity = "critical" AND status = "new"')
    critical_alerts = cursor.fetchone()[0]
    
    # إحصائيات المستخدمين
    cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
    active_users = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        'cameras': {
            'total': total_cameras,
            'online': online_cameras,
            'offline': total_cameras - online_cameras,
            'recording': recording_cameras,
            'motion_detection': motion_cameras
        },
        'recordings': {
            'total': total_recordings,
            'today': today_recordings,
            'storage_mb': round(total_storage / (1024 * 1024), 2),
            'total_hours': round(total_duration / 3600, 1)
        },
        'alerts': {
            'unread': unread_alerts,
            'today': today_alerts,
            'critical': critical_alerts
        },
        'users': {
            'active': active_users
        }
    }

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def format_duration(seconds):
    """تنسيق المدة الزمنية"""
    if not seconds:
        return "00:00:00"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    secs = seconds % 60
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def get_alert_icon(alert_type):
    """الحصول على أيقونة التنبيه"""
    icons = {
        'motion': 'fas fa-running',
        'connection': 'fas fa-wifi',
        'system': 'fas fa-cog',
        'recording': 'fas fa-video',
        'storage': 'fas fa-hdd'
    }
    return icons.get(alert_type, 'fas fa-bell')

def get_severity_color(severity):
    """الحصول على لون الخطورة"""
    colors = {
        'info': 'info',
        'warning': 'warning', 
        'medium': 'primary',
        'high': 'danger',
        'critical': 'dark'
    }
    return colors.get(severity, 'secondary')

# تسجيل المرشحات في Jinja2
app.jinja_env.filters['format_file_size'] = format_file_size
app.jinja_env.filters['format_duration'] = format_duration
app.jinja_env.filters['get_alert_icon'] = get_alert_icon
app.jinja_env.filters['get_severity_color'] = get_severity_color

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة الكاميرات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .hero-card { border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.2); backdrop-filter: blur(10px); background: rgba(255,255,255,0.95); }
        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
        .btn-gradient { background: linear-gradient(45deg, #667eea, #764ba2); border: none; }
        .btn-gradient:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.3); }
        .feature-card { transition: all 0.3s ease; }
        .feature-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card hero-card">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-video feature-icon text-primary"></i>
                        </div>
                        <h1 class="display-4 mb-3 text-gradient">نظام مراقبة الكاميرات المتقدم</h1>
                        <p class="lead mb-4 text-muted">نظام شامل ومتطور لإدارة ومراقبة الكاميرات الأمنية مع جميع المميزات المتقدمة</p>

                        <div class="row mb-5">
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-camera fa-2x text-primary mb-2"></i>
                                        <h6>دعم جميع الكاميرات</h6>
                                        <small class="text-muted">IP, DVR, NVR, ONVIF</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-eye fa-2x text-success mb-2"></i>
                                        <h6>بث مباشر متقدم</h6>
                                        <small class="text-muted">جودة عالية ومتعدد</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-record-vinyl fa-2x text-warning mb-2"></i>
                                        <h6>تسجيل ذكي</h6>
                                        <small class="text-muted">كشف حركة متقدم</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bell fa-2x text-danger mb-2"></i>
                                        <h6>تنبيهات شاملة</h6>
                                        <small class="text-muted">نظام إنذار متطور</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mb-4">
                            <h5><i class="fas fa-star me-2"></i>النظام المتقدم يتضمن:</h5>
                            <div class="row text-start">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>دعم جميع أنواع الكاميرات</li>
                                        <li><i class="fas fa-check text-success me-2"></i>بث مباشر عالي الجودة</li>
                                        <li><i class="fas fa-check text-success me-2"></i>تسجيل تلقائي ومجدول</li>
                                        <li><i class="fas fa-check text-success me-2"></i>كشف الحركة المتقدم</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>نظام تنبيهات شامل</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة المستخدمين</li>
                                        <li><i class="fas fa-check text-success me-2"></i>أرشيف منظم</li>
                                        <li><i class="fas fa-check text-success me-2"></i>واجهة عربية متطورة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{{ url_for('login') }}" class="btn btn-gradient btn-lg px-5">
                                <i class="fas fa-sign-in-alt me-2"></i>دخول النظام
                            </a>
                            <button class="btn btn-outline-secondary btn-lg px-4" onclick="showFeatures()">
                                <i class="fas fa-list me-2"></i>المميزات الكاملة
                            </button>
                        </div>

                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>المستخدم: admin |
                                <i class="fas fa-key me-1"></i>كلمة المرور: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showFeatures() {
            alert('🎉 مميزات النظام المتقدم:\\n\\n📹 إدارة الكاميرات:\\n• دعم IP, DVR, NVR\\n• بروتوكولات RTSP, HTTP, ONVIF\\n• مراقبة حالة الاتصال\\n• إعدادات متقدمة\\n\\n🎥 البث والتسجيل:\\n• بث مباشر عالي الجودة\\n• تسجيل تلقائي ومجدول\\n• كشف الحركة الذكي\\n• أرشفة منظمة\\n\\n🔔 نظام التنبيهات:\\n• تنبيهات كشف الحركة\\n• تنبيهات انقطاع الاتصال\\n• تنبيهات النظام\\n• تصنيف حسب الأولوية\\n\\n👥 إدارة المستخدمين:\\n• صلاحيات متدرجة\\n• نظام أمان متقدم\\n• سجل العمليات\\n\\n📊 التقارير والإحصائيات:\\n• إحصائيات شاملة\\n• تقارير مفصلة\\n• رسوم بيانية\\n• تحليلات متقدمة');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.hero-card');
            card.style.transform = 'translateY(30px)';
            card.style.opacity = '0';

            setTimeout(() => {
                card.style.transition = 'all 1s ease';
                card.style.transform = 'translateY(0)';
                card.style.opacity = '1';
            }, 200);

            // تأثير على البطاقات
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
    ''')

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = check_login(username, password)
        if user:
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['full_name'] = user[2]
            session['is_admin'] = user[3]
            session['can_view_cameras'] = user[4]
            session['can_control_cameras'] = user[5]
            session['can_manage_recordings'] = user[6]
            session['can_manage_users'] = user[7]
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-card { border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .form-control:focus { border-color: #667eea; box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-4">
                <div class="card login-card">
                    <div class="card-header text-center bg-primary text-white">
                        <i class="fas fa-video fa-3x mb-2"></i>
                        <h4>تسجيل الدخول</h4>
                        <p class="mb-0 small">نظام مراقبة الكاميرات المتقدم</p>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" name="username" class="form-control" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" name="password" class="form-control" id="password" value="admin123" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember">
                                <label class="form-check-label" for="remember">تذكرني</label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <a href="{{ url_for('index') }}" class="text-decoration-none">
                                <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                            </a>
                        </div>

                        <div class="mt-3 p-2 bg-light rounded">
                            <small class="text-muted">
                                <strong>بيانات تجريبية:</strong><br>
                                المستخدم: admin<br>
                                كلمة المرور: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
    ''')

# لوحة التحكم المتقدمة
@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    stats = get_stats()
    cameras = get_cameras()
    recent_recordings = get_recordings()[:5]  # آخر 5 تسجيلات
    recent_alerts = get_alerts()[:5]  # آخر 5 تنبيهات

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام مراقبة الكاميرات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .stats-card { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px; transition: transform 0.3s; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-card.bg-success { background: linear-gradient(135deg, #28a745, #20c997) !important; }
        .stats-card.bg-danger { background: linear-gradient(135deg, #dc3545, #fd7e14) !important; }
        .stats-card.bg-warning { background: linear-gradient(135deg, #ffc107, #fd7e14) !important; }
        .stats-card.bg-info { background: linear-gradient(135deg, #17a2b8, #6f42c1) !important; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .camera-card { border-radius: 10px; transition: all 0.3s; }
        .camera-card:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .alert-item { border-left: 4px solid; transition: all 0.3s; }
        .alert-item:hover { background-color: #f8f9fa; }
        .recording-item { transition: all 0.3s; }
        .recording-item:hover { background-color: #f8f9fa; }
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .status-connecting { color: #ffc107; }
    </style>
</head>
<body>
    <!-- شريط التنقل المتقدم -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    {% if session.can_view_cameras %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-camera me-1"></i>الكاميرات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('cameras') }}">قائمة الكاميرات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('live_view') }}">المشاهدة المباشرة</a></li>
                            {% if session.can_control_cameras %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_camera') }}">إضافة كاميرا</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}
                    {% if session.can_manage_recordings %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-video me-1"></i>التسجيلات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('recordings') }}">جميع التسجيلات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('recordings_search') }}">البحث في التسجيلات</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('alerts') }}">
                            <i class="fas fa-bell me-1"></i>التنبيهات
                            {% if stats.alerts.unread > 0 %}
                            <span class="badge bg-danger">{{ stats.alerts.unread }}</span>
                            {% endif %}
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ session.full_name or session.username }}
                            {% if session.is_admin %}
                            <span class="badge bg-warning text-dark">مدير</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">{{ session.username }}</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            {% if session.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('users') }}"><i class="fas fa-users me-2"></i>إدارة المستخدمين</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>إعدادات النظام</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم المتقدمة</h2>
                        <p class="text-muted mb-0">مرحباً {{ session.full_name or session.username }}، هذه نظرة شاملة على النظام</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                        <span class="text-muted small">آخر تحديث: <span id="lastUpdate">الآن</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.cameras.total }}</h3>
                            <p class="mb-0">إجمالي الكاميرات</p>
                            <small class="opacity-75">{{ stats.cameras.motion_detection }} مع كشف الحركة</small>
                        </div>
                        <i class="fas fa-camera fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-success p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.cameras.online }}</h3>
                            <p class="mb-0">كاميرات متصلة</p>
                            <small class="opacity-75">{{ stats.cameras.recording }} تسجل الآن</small>
                        </div>
                        <i class="fas fa-wifi fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-warning p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.recordings.total }}</h3>
                            <p class="mb-0">إجمالي التسجيلات</p>
                            <small class="opacity-75">{{ stats.recordings.storage_mb }} ميجابايت</small>
                        </div>
                        <i class="fas fa-video fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-danger p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.alerts.unread }}</h3>
                            <p class="mb-0">تنبيهات جديدة</p>
                            <small class="opacity-75">{{ stats.alerts.critical }} حرجة</small>
                        </div>
                        <i class="fas fa-bell fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="row">
            <!-- الكاميرات -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-camera me-2"></i>حالة الكاميرات
                        </h5>
                        {% if session.can_view_cameras %}
                        <a href="{{ url_for('cameras') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                        <div class="row">
                            {% for camera in cameras[:6] %}
                            <div class="col-md-6 col-lg-4 mb-3" data-camera-id="{{ camera[0] }}">
                                <div class="card camera-card h-100">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-1">{{ camera[1] }}</h6>
                                            <span class="badge bg-{{ 'success' if camera[10] == 'online' else 'danger' if camera[10] == 'offline' else 'warning' }}">
                                                {% if camera[10] == 'online' %}
                                                <i class="fas fa-circle me-1"></i>متصل
                                                {% elif camera[10] == 'offline' %}
                                                <i class="fas fa-times-circle me-1"></i>غير متصل
                                                {% else %}
                                                <i class="fas fa-clock me-1"></i>يتصل
                                                {% endif %}
                                            </span>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-map-marker-alt me-1"></i>{{ camera[10] or 'غير محدد' }}</div>
                                            <div><i class="fas fa-network-wired me-1"></i>{{ camera[3] }}:{{ camera[4] }}
                                                {% if camera[6] in ['DVR', 'NVR'] %} - قناة {{ camera[5] }}{% endif %}
                                            </div>
                                            <div><i class="fas fa-tag me-1"></i>{{ camera[6] }} - {{ camera[7] }}</div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="small">
                                                {% if camera[11] %}
                                                <span class="badge bg-danger"><i class="fas fa-record-vinyl me-1"></i>يسجل</span>
                                                {% endif %}
                                                {% if camera[12] %}
                                                <span class="badge bg-warning"><i class="fas fa-running me-1"></i>كشف حركة</span>
                                                {% endif %}
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                {% if session.can_view_cameras %}
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewCamera({{ camera[0] }})" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% endif %}
                                                {% if session.can_control_cameras and camera[10] == 'online' %}
                                                <button class="btn btn-outline-success btn-sm" onclick="liveStream({{ camera[0] }})" title="بث مباشر">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                {% endif %}
                                                {% if session.can_control_cameras %}
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteCamera({{ camera[0] }}, '{{ camera[1] }}')" title="حذف الكاميرا">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد كاميرات</h5>
                            <p class="text-muted">ابدأ بإضافة كاميرا جديدة للنظام</p>
                            {% if session.can_control_cameras %}
                            <button class="btn btn-primary" onclick="addCamera()">
                                <i class="fas fa-plus me-1"></i>إضافة كاميرا
                            </button>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- التنبيهات الحديثة -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>التنبيهات الحديثة
                        </h5>
                        <a href="{{ url_for('alerts') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                        {% if recent_alerts %}
                        {% for alert in recent_alerts %}
                        <div class="alert-item p-3 border-bottom border-{{ alert[4] | get_severity_color }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="{{ alert[3] | get_alert_icon }} text-{{ alert[4] | get_severity_color }} me-2"></i>
                                        <h6 class="mb-0">{{ alert[1] }}</h6>
                                    </div>
                                    <p class="small text-muted mb-1">{{ alert[2] }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            {% if alert[8] %}{{ alert[8] }} - {% endif %}
                                            {{ alert[7] }}
                                        </small>
                                        <span class="badge bg-{{ alert[4] | get_severity_color }}">{{ alert[4] }}</span>
                                    </div>
                                </div>
                                {% if not alert[6] %}
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="markAsRead({{ alert[0] }})">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">لا توجد تنبيهات حديثة</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- التسجيلات الحديثة -->
        {% if session.can_manage_recordings %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-video me-2"></i>التسجيلات الحديثة
                        </h5>
                        <a href="{{ url_for('recordings') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        {% if recent_recordings %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>الكاميرا</th>
                                        <th>النوع</th>
                                        <th>المدة</th>
                                        <th>الحجم</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for recording in recent_recordings %}
                                    <tr class="recording-item">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-video text-primary me-2"></i>
                                                <div>
                                                    <div class="fw-bold">{{ recording[1] }}</div>
                                                    {% if recording[9] %}
                                                    <small class="text-warning"><i class="fas fa-shield-alt me-1"></i>محمي</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ recording[10] }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'primary' if recording[4] == 'manual' else 'success' if recording[4] == 'scheduled' else 'warning' }}">
                                                {% if recording[4] == 'manual' %}يدوي
                                                {% elif recording[4] == 'scheduled' %}مجدول
                                                {% elif recording[4] == 'motion' %}كشف حركة
                                                {% else %}{{ recording[4] }}
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>{{ recording[3] | format_duration }}</td>
                                        <td>{{ recording[2] | format_file_size }}</td>
                                        <td>
                                            <small class="text-muted">{{ recording[6] }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if recording[8] == 'completed' else 'warning' }}">
                                                {{ 'مكتمل' if recording[8] == 'completed' else recording[8] }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="playRecording({{ recording[0] }})">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadRecording({{ recording[0] }})">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-video fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تسجيلات</h5>
                            <p class="text-muted">ابدأ التسجيل من الكاميرات لرؤية التسجيلات هنا</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- معلومات النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5><i class="fas fa-check-circle me-2"></i>النظام المتقدم يعمل بنجاح!</h5>
                            <p class="mb-0">
                                نظام مراقبة الكاميرات المتقدم جاهز للعمل مع جميع المميزات المطلوبة.
                                تم تطوير هذا النظام بـ Python Flask مع دعم كامل للغة العربية وجميع الوظائف المتقدمة.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="small text-muted">
                                <div><strong>إجمالي الساعات المسجلة:</strong> {{ stats.recordings.total_hours }} ساعة</div>
                                <div><strong>مساحة التخزين:</strong> {{ stats.recordings.storage_mb }} ميجابايت</div>
                                <div><strong>المستخدمين النشطين:</strong> {{ stats.users.active }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث لوحة التحكم
        function refreshDashboard() {
            location.reload();
        }

        // عرض تفاصيل الكاميرا
        function viewCamera(cameraId) {
            alert('عرض تفاصيل الكاميرا رقم: ' + cameraId + '\\n\\nسيتم فتح صفحة تفاصيل الكاميرا مع:\\n• معلومات الاتصال\\n• إعدادات التسجيل\\n• سجل الأنشطة\\n• إحصائيات الاستخدام');
        }

        // بث مباشر
        function liveStream(cameraId) {
            alert('بدء البث المباشر للكاميرا رقم: ' + cameraId + '\\n\\nسيتم فتح نافذة البث المباشر مع:\\n• بث عالي الجودة\\n• أدوات التحكم\\n• التقاط الصور\\n• تسجيل مقاطع');
        }

        // إضافة كاميرا
        function addCamera() {
            alert('إضافة كاميرا جديدة\\n\\nسيتم فتح نموذج إضافة كاميرا مع:\\n• معلومات الاتصال\\n• إعدادات البروتوكول\\n• إعدادات التسجيل\\n• اختبار الاتصال');
        }

        // تشغيل التسجيل
        function playRecording(recordingId) {
            alert('تشغيل التسجيل رقم: ' + recordingId + '\\n\\nسيتم فتح مشغل الفيديو مع:\\n• تحكم كامل في التشغيل\\n• إمكانية التقديم والإرجاع\\n• عرض ملء الشاشة\\n• معلومات التسجيل');
        }

        // تحميل التسجيل
        function downloadRecording(recordingId) {
            alert('تحميل التسجيل رقم: ' + recordingId + '\\n\\nسيتم بدء تحميل الملف...');
        }

        // تمييز التنبيه كمقروء
        function markAsRead(alertId) {
            alert('تم تمييز التنبيه رقم ' + alertId + ' كمقروء');
            // هنا يمكن إضافة AJAX call لتحديث قاعدة البيانات
        }

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.getElementById('lastUpdate').textContent = timeString;
        }

        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            updateTime();
            // يمكن إضافة تحديث للإحصائيات هنا
        }, 30000);

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على البطاقات
            const cards = document.querySelectorAll('.camera-card, .stats-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });

            updateTime();
        });

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                const alert = document.querySelector('.alert:last-of-type');
                if (alert) alert.remove();
            }, 5000);
        }
    </script>
</body>
</html>
    ''', stats=stats, cameras=cameras, recent_recordings=recent_recordings, recent_alerts=recent_alerts, session=session)

# المسارات الإضافية
@app.route('/cameras')
def cameras():
    if 'user_id' not in session or not session.get('can_view_cameras'):
        flash('غير مسموح لك بعرض الكاميرات', 'error')
        return redirect(url_for('dashboard'))

    cameras = get_cameras()
    stats = get_stats()

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الكاميرات - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .camera-card {
            border-radius: 15px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .camera-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .status-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 10;
        }
        .camera-image {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .camera-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .action-buttons .btn {
            margin: 2px;
        }
        .stats-mini {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-camera me-2"></i>قائمة الكاميرات</h2>
                        <p class="text-muted mb-0">إدارة شاملة لجميع الكاميرات مع إمكانيات التحكم الكاملة</p>
                    </div>
                    <div>
                        {% if session.can_control_cameras %}
                        <button class="btn btn-success me-2" onclick="addCamera()">
                            <i class="fas fa-plus me-1"></i>إضافة كاميرا
                        </button>
                        {% endif %}
                        <button class="btn btn-outline-primary" onclick="refreshCameras()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-mini text-center">
                    <h3>{{ stats.cameras.total }}</h3>
                    <p class="mb-0">إجمالي الكاميرات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-mini text-center" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <h3>{{ stats.cameras.online }}</h3>
                    <p class="mb-0">متصلة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-mini text-center" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                    <h3>{{ stats.cameras.recording }}</h3>
                    <p class="mb-0">تسجل الآن</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-mini text-center" style="background: linear-gradient(135deg, #17a2b8, #6f42c1);">
                    <h3>{{ stats.cameras.motion_detection }}</h3>
                    <p class="mb-0">كشف الحركة</p>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والفلترة -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control search-box" placeholder="البحث في الكاميرات..." id="searchInput" onkeyup="searchCameras()">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter" onchange="filterCameras()">
                        <option value="">جميع الحالات</option>
                        <option value="online">متصلة</option>
                        <option value="offline">غير متصلة</option>
                        <option value="connecting">يتصل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="typeFilter" onchange="filterCameras()">
                        <option value="">جميع الأنواع</option>
                        <option value="IP">كاميرا IP</option>
                        <option value="DVR">جهاز DVR</option>
                        <option value="NVR">جهاز NVR</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="recordingFilter" onchange="filterCameras()">
                        <option value="">جميع التسجيلات</option>
                        <option value="recording">تسجل الآن</option>
                        <option value="not-recording">لا تسجل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>مسح الفلاتر
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة الكاميرات -->
        <div class="row" id="camerasContainer">
            {% if cameras %}
            {% for camera in cameras %}
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4 camera-item"
                 data-camera-id="{{ camera[0] }}"
                 data-name="{{ camera[1] }}"
                 data-status="{{ camera[10] }}"
                 data-type="{{ camera[5] }}"
                 data-recording="{{ 'recording' if camera[11] else 'not-recording' }}">
                <div class="card camera-card h-100">
                    <div class="card-body p-3 position-relative">
                        <!-- شارة الحالة -->
                        <span class="badge status-badge bg-{{ 'success' if camera[10] == 'online' else 'danger' if camera[10] == 'offline' else 'warning' }}">
                            {% if camera[10] == 'online' %}
                            <i class="fas fa-circle me-1"></i>متصل
                            {% elif camera[10] == 'offline' %}
                            <i class="fas fa-times-circle me-1"></i>غير متصل
                            {% else %}
                            <i class="fas fa-clock me-1"></i>يتصل
                            {% endif %}
                        </span>

                        <!-- صورة الكاميرا -->
                        <div class="camera-image">
                            <i class="fas fa-camera"></i>
                        </div>

                        <!-- معلومات الكاميرا -->
                        <h5 class="card-title mb-2">{{ camera[1] }}</h5>
                        <p class="text-muted small mb-3">{{ camera[2] or 'لا يوجد وصف' }}</p>

                        <!-- تفاصيل تقنية -->
                        <div class="camera-info">
                            <div class="row small">
                                <div class="col-6">
                                    <div><i class="fas fa-map-marker-alt text-primary me-1"></i>{{ camera[10] or 'غير محدد' }}</div>
                                    <div><i class="fas fa-network-wired text-info me-1"></i>{{ camera[3] }}:{{ camera[4] }}
                                        {% if camera[6] in ['DVR', 'NVR'] %}<br><small>قناة {{ camera[5] }}</small>{% endif %}
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div><i class="fas fa-tag text-warning me-1"></i>{{ camera[6] }}</div>
                                    <div><i class="fas fa-wifi text-success me-1"></i>{{ camera[7] }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الماركة والموديل -->
                        {% if camera[7] or camera[8] %}
                        <div class="mb-2">
                            <small class="text-muted">
                                {% if camera[7] %}<strong>{{ camera[7] }}</strong>{% endif %}
                                {% if camera[8] %} - {{ camera[8] }}{% endif %}
                            </small>
                        </div>
                        {% endif %}

                        <!-- شارات الميزات -->
                        <div class="mb-3">
                            {% if camera[11] %}
                            <span class="badge bg-danger me-1">
                                <i class="fas fa-record-vinyl me-1"></i>يسجل الآن
                            </span>
                            {% endif %}
                            {% if camera[12] %}
                            <span class="badge bg-warning me-1">
                                <i class="fas fa-running me-1"></i>كشف الحركة
                            </span>
                            {% endif %}
                            <span class="badge bg-info">
                                <i class="fas fa-hd-video me-1"></i>{{ camera[13] or 'متوسط' }}
                            </span>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="action-buttons d-grid gap-2">
                            {% if session.can_view_cameras %}
                            <div class="btn-group">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewCamera({{ camera[0] }})">
                                    <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                </button>
                                {% if camera[10] == 'online' %}
                                <button class="btn btn-outline-success btn-sm" onclick="liveStream({{ camera[0] }})">
                                    <i class="fas fa-play me-1"></i>بث مباشر
                                </button>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if session.can_control_cameras %}
                            <div class="btn-group">
                                {% if camera[10] == 'online' %}
                                <button class="btn btn-outline-warning btn-sm" onclick="takeSnapshot({{ camera[0] }})">
                                    <i class="fas fa-camera me-1"></i>التقاط صورة
                                </button>
                                <button class="btn btn-outline-{{ 'danger' if camera[11] else 'success' }} btn-sm" onclick="toggleRecording({{ camera[0] }}, {{ camera[11] }})">
                                    <i class="fas fa-{{ 'stop' if camera[11] else 'record-vinyl' }} me-1"></i>
                                    {{ 'إيقاف التسجيل' if camera[11] else 'بدء التسجيل' }}
                                </button>
                                {% endif %}
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm" onclick="editCamera({{ camera[0] }})">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="testConnection({{ camera[0] }})">
                                    <i class="fas fa-plug me-1"></i>اختبار الاتصال
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteCamera({{ camera[0] }}, '{{ camera[1] }}')" title="حذف الكاميرا">
                                    <i class="fas fa-trash me-1"></i>حذف
                                </button>
                            </div>
                            {% endif %}
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="mt-2">
                            <small class="text-muted">
                                <div>تم الإضافة: {{ camera[14] }}</div>
                                {% if camera[15] %}
                                <div>آخر اتصال: {{ camera[15] }}</div>
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-camera fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">لا توجد كاميرات في النظام</h3>
                    <p class="text-muted mb-4">ابدأ بإضافة كاميرا جديدة للنظام لبدء المراقبة</p>
                    {% if session.can_control_cameras %}
                    <button class="btn btn-primary btn-lg" onclick="addCamera()">
                        <i class="fas fa-plus me-2"></i>إضافة أول كاميرا
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- رسالة عدم وجود نتائج بحث -->
        <div id="noResults" class="text-center py-5" style="display: none;">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد نتائج</h4>
            <p class="text-muted">لم يتم العثور على كاميرات تطابق معايير البحث</p>
            <button class="btn btn-outline-primary" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>مسح الفلاتر
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البحث في الكاميرات
        function searchCameras() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const cameras = document.querySelectorAll('.camera-item');
            let visibleCount = 0;

            cameras.forEach(camera => {
                const name = camera.getAttribute('data-name').toLowerCase();
                if (name.includes(searchTerm)) {
                    camera.style.display = 'block';
                    visibleCount++;
                } else {
                    camera.style.display = 'none';
                }
            });

            toggleNoResults(visibleCount === 0);
        }

        // فلترة الكاميرات
        function filterCameras() {
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const recordingFilter = document.getElementById('recordingFilter').value;
            const cameras = document.querySelectorAll('.camera-item');
            let visibleCount = 0;

            cameras.forEach(camera => {
                let show = true;

                if (statusFilter && camera.getAttribute('data-status') !== statusFilter) {
                    show = false;
                }
                if (typeFilter && camera.getAttribute('data-type') !== typeFilter) {
                    show = false;
                }
                if (recordingFilter && camera.getAttribute('data-recording') !== recordingFilter) {
                    show = false;
                }

                if (show) {
                    camera.style.display = 'block';
                    visibleCount++;
                } else {
                    camera.style.display = 'none';
                }
            });

            toggleNoResults(visibleCount === 0);
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('recordingFilter').value = '';

            const cameras = document.querySelectorAll('.camera-item');
            cameras.forEach(camera => {
                camera.style.display = 'block';
            });

            toggleNoResults(false);
        }

        // إظهار/إخفاء رسالة عدم وجود نتائج
        function toggleNoResults(show) {
            const noResults = document.getElementById('noResults');
            const camerasContainer = document.getElementById('camerasContainer');

            if (show) {
                noResults.style.display = 'block';
                camerasContainer.style.display = 'none';
            } else {
                noResults.style.display = 'none';
                camerasContainer.style.display = 'flex';
            }
        }

        // تحديث قائمة الكاميرات
        function refreshCameras() {
            showNotification('جاري تحديث قائمة الكاميرات...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // عرض تفاصيل الكاميرا
        function viewCamera(cameraId) {
            showNotification('فتح تفاصيل الكاميرا رقم: ' + cameraId, 'info');
            // هنا يمكن فتح modal أو صفحة جديدة
        }

        // بث مباشر
        function liveStream(cameraId) {
            showNotification('بدء البث المباشر للكاميرا رقم: ' + cameraId, 'success');
            // هنا يمكن فتح نافذة البث المباشر
        }

        // التقاط صورة
        function takeSnapshot(cameraId) {
            showNotification('جاري التقاط صورة من الكاميرا رقم: ' + cameraId, 'info');
            setTimeout(() => {
                showNotification('تم التقاط الصورة بنجاح!', 'success');
            }, 2000);
        }

        // تبديل التسجيل
        function toggleRecording(cameraId, isRecording) {
            const action = isRecording ? 'إيقاف' : 'بدء';
            showNotification('جاري ' + action + ' التسجيل للكاميرا رقم: ' + cameraId, 'info');

            setTimeout(() => {
                showNotification('تم ' + action + ' التسجيل بنجاح!', 'success');
                // هنا يمكن تحديث حالة الزر
            }, 2000);
        }

        // تعديل الكاميرا
        function editCamera(cameraId) {
            showNotification('فتح إعدادات الكاميرا رقم: ' + cameraId, 'info');
            // هنا يمكن فتح نموذج التعديل
        }

        // اختبار الاتصال
        function testConnection(cameraId) {
            showNotification('جاري اختبار الاتصال للكاميرا رقم: ' + cameraId, 'info');

            setTimeout(() => {
                const success = Math.random() > 0.3; // محاكاة نتيجة الاختبار
                if (success) {
                    showNotification('الاتصال ناجح! الكاميرا تعمل بشكل طبيعي', 'success');
                } else {
                    showNotification('فشل الاتصال! تحقق من إعدادات الشبكة', 'error');
                }
            }, 3000);
        }

        // حذف الكاميرا
        function deleteCamera(cameraId, cameraName) {
            // تأكيد الحذف مع رسالة تحذيرية
            const confirmMessage = `هل أنت متأكد من حذف الكاميرا "${cameraName}"؟\n\n⚠️ تحذير:\n• سيتم حذف جميع التسجيلات المرتبطة بهذه الكاميرا\n• لا يمكن التراجع عن هذا الإجراء\n• تأكد من عمل نسخة احتياطية إذا لزم الأمر\n\nاكتب "حذف" للتأكيد:`;

            const userInput = prompt(confirmMessage);

            if (userInput !== 'حذف') {
                if (userInput !== null) { // المستخدم لم يضغط إلغاء
                    showNotification('تم إلغاء عملية الحذف - يجب كتابة "حذف" للتأكيد', 'warning');
                }
                return;
            }

            // تأكيد إضافي
            if (!confirm(`تأكيد أخير: هل تريد فعلاً حذف الكاميرا "${cameraName}" نهائياً؟`)) {
                showNotification('تم إلغاء عملية الحذف', 'info');
                return;
            }

            // إظهار رسالة التحميل
            showNotification('جاري حذف الكاميرا...', 'warning');

            // إرسال طلب الحذف
            fetch(`/delete_camera/${cameraId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');

                    // إزالة بطاقة الكاميرا من الواجهة
                    const cameraCard = document.querySelector(`[data-camera-id="${cameraId}"]`);
                    if (cameraCard) {
                        cameraCard.style.transition = 'all 0.5s ease';
                        cameraCard.style.opacity = '0';
                        cameraCard.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            cameraCard.remove();

                            // تحديث العداد إذا كان موجوداً
                            const cameraCount = document.querySelectorAll('.camera-card').length;
                            const countElement = document.querySelector('.camera-count');
                            if (countElement) {
                                countElement.textContent = cameraCount;
                            }

                            // إظهار رسالة إذا لم تعد هناك كاميرات
                            if (cameraCount === 0) {
                                const container = document.getElementById('camerasContainer');
                                if (container) {
                                    container.innerHTML = `
                                        <div class="col-12 text-center py-5">
                                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد كاميرات</h5>
                                            <p class="text-muted">لم يتم إضافة أي كاميرات بعد</p>
                                            <a href="/add_camera" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>إضافة كاميرا جديدة
                                            </a>
                                        </div>
                                    `;
                                }
                            }
                        }, 500);
                    } else {
                        // إعادة تحميل الصفحة إذا لم نجد البطاقة
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ أثناء حذف الكاميرا', 'error');
            });
        }

        // إضافة كاميرا جديدة
        function addCamera() {
            showNotification('فتح نموذج إضافة كاميرا جديدة', 'info');
            // هنا يمكن فتح نموذج الإضافة
        }

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.camera-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // تحديث تلقائي لحالة الكاميرات كل دقيقة
        setInterval(function() {
            // هنا يمكن إضافة AJAX call لتحديث حالة الكاميرات
            console.log('تحديث حالة الكاميرات...');
        }, 60000);
    </script>
</body>
</html>
    ''', cameras=cameras, stats=stats, session=session)

@app.route('/live_view')
def live_view():
    if 'user_id' not in session or not session.get('can_view_cameras'):
        flash('غير مسموح لك بالمشاهدة المباشرة', 'error')
        return redirect(url_for('dashboard'))

    # الحصول على الكاميرات المتصلة فقط
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, name, description, ip_address, port, channel, camera_type, protocol, brand, model, location,
               status, is_recording, motion_detection, recording_quality
        FROM cameras WHERE status = 'online'
        ORDER BY created_at DESC
    ''')
    online_cameras = cursor.fetchall()
    conn.close()

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المشاهدة المباشرة - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #000; color: white; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.5); }
        .video-container {
            position: relative;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            border: 2px solid #333;
            transition: all 0.3s ease;
        }
        .video-container:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }
        .video-container.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            border-radius: 0;
            transform: none;
        }
        .video-placeholder {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .video-placeholder.large {
            height: 500px;
        }
        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .video-container:hover .video-overlay {
            opacity: 1;
        }
        .video-controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            border-radius: 5px;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .camera-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            border-radius: 5px;
            padding: 5px 10px;
        }
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .control-panel {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }
        .btn-stream {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        .btn-stream:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
        .grid-1x1 .video-placeholder { height: 500px; }
        .grid-2x2 .video-placeholder { height: 300px; }
        .grid-3x3 .video-placeholder { height: 200px; }
        .grid-4x4 .video-placeholder { height: 150px; }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera me-1"></i>قائمة الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-eye me-2"></i>المشاهدة المباشرة</h2>
                        <p class="text-muted mb-0">شاشات البث المباشر لجميع الكاميرات المتصلة</p>
                    </div>
                    <div>
                        <span class="badge bg-success me-2">
                            <i class="fas fa-circle me-1"></i>{{ online_cameras|length }} كاميرا متصلة
                        </span>
                        <button class="btn btn-outline-light" onclick="refreshStreams()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة التحكم -->
        <div class="control-panel">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <label class="form-label text-white">تخطيط الشاشة:</label>
                    <select class="form-select" id="gridLayout" onchange="changeLayout()">
                        <option value="1x1">شاشة واحدة (1×1)</option>
                        <option value="2x2" selected>4 شاشات (2×2)</option>
                        <option value="3x3">9 شاشات (3×3)</option>
                        <option value="4x4">16 شاشة (4×4)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label text-white">جودة البث:</label>
                    <select class="form-select" id="streamQuality" onchange="changeQuality()">
                        <option value="low">منخفضة (720p)</option>
                        <option value="medium" selected>متوسطة (1080p)</option>
                        <option value="high">عالية (4K)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label text-white" for="autoRefresh">
                            تحديث تلقائي
                        </label>
                    </div>
                </div>
                <div class="col-md-3 text-end">
                    <button class="btn btn-stream me-2" onclick="startAllStreams()">
                        <i class="fas fa-play me-1"></i>تشغيل الكل
                    </button>
                    <button class="btn btn-outline-danger" onclick="stopAllStreams()">
                        <i class="fas fa-stop me-1"></i>إيقاف الكل
                    </button>
                </div>
            </div>
        </div>

        <!-- شاشات البث المباشر -->
        <div class="row" id="streamsContainer">
            {% if online_cameras %}
            {% for camera in online_cameras %}
            <div class="col-lg-6 col-xl-6 stream-item grid-2x2" data-camera-id="{{ camera[0] }}">
                <div class="video-container">
                    <!-- مؤشر الحالة -->
                    <div class="status-indicator"></div>

                    <!-- معلومات الكاميرا -->
                    <div class="camera-info">
                        <div class="small">
                            <strong>{{ camera[1] }}</strong><br>
                            <span class="text-muted">{{ camera[9] or 'غير محدد' }}</span>
                        </div>
                    </div>

                    <!-- منطقة الفيديو -->
                    <div class="video-placeholder" id="video-{{ camera[0] }}">
                        <div class="text-center">
                            <i class="fas fa-camera fa-4x mb-3"></i>
                            <h5>{{ camera[1] }}</h5>
                            <p class="text-muted">{{ camera[9] or 'غير محدد' }}</p>
                            <button class="btn btn-stream" onclick="startStream({{ camera[0] }})">
                                <i class="fas fa-play me-1"></i>بدء البث
                            </button>
                        </div>
                    </div>

                    <!-- طبقة التحكم -->
                    <div class="video-overlay">
                        <div class="text-center">
                            <button class="btn btn-light btn-sm me-2" onclick="toggleFullscreen({{ camera[0] }})">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button class="btn btn-light btn-sm me-2" onclick="takeSnapshot({{ camera[0] }})">
                                <i class="fas fa-camera"></i>
                            </button>
                            <button class="btn btn-light btn-sm me-2" onclick="toggleRecording({{ camera[0] }})">
                                <i class="fas fa-record-vinyl"></i>
                            </button>
                            <button class="btn btn-light btn-sm" onclick="stopStream({{ camera[0] }})">
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>
                    </div>

                    <!-- أدوات التحكم -->
                    <div class="video-controls">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-circle me-1"></i>مباشر
                            </span>
                            {% if camera[11] %}
                            <span class="badge bg-danger me-2">
                                <i class="fas fa-record-vinyl me-1"></i>يسجل
                            </span>
                            {% endif %}
                            {% if camera[12] %}
                            <span class="badge bg-warning me-2">
                                <i class="fas fa-running me-1"></i>كشف حركة
                            </span>
                            {% endif %}
                        </div>
                        <div>
                            <small class="text-muted">{{ camera[14] or 'متوسط' }} | {{ camera[3] }}:{{ camera[4] }}
                                {% if camera[6] in ['DVR', 'NVR'] %} - قناة {{ camera[5] }}{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-video-slash fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">لا توجد كاميرات متصلة</h3>
                    <p class="text-muted mb-4">لا يمكن عرض البث المباشر لأنه لا توجد كاميرات متصلة حالياً</p>
                    <a href="{{ url_for('cameras') }}" class="btn btn-primary">
                        <i class="fas fa-camera me-1"></i>إدارة الكاميرات
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- معلومات إضافية -->
        {% if online_cameras %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="control-panel">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4>{{ online_cameras|length }}</h4>
                            <p class="text-muted mb-0">كاميرات متصلة</p>
                        </div>
                        <div class="col-md-3">
                            <h4 id="activeStreams">0</h4>
                            <p class="text-muted mb-0">بث نشط</p>
                        </div>
                        <div class="col-md-3">
                            <h4 id="recordingCount">{{ online_cameras|selectattr('11')|list|length }}</h4>
                            <p class="text-muted mb-0">تسجل الآن</p>
                        </div>
                        <div class="col-md-3">
                            <h4 id="totalBandwidth">0 MB/s</h4>
                            <p class="text-muted mb-0">استهلاك البيانات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let activeStreams = 0;
        let isFullscreen = false;

        // تغيير تخطيط الشاشة
        function changeLayout() {
            const layout = document.getElementById('gridLayout').value;
            const container = document.getElementById('streamsContainer');
            const items = document.querySelectorAll('.stream-item');

            // إزالة الفئات القديمة
            items.forEach(item => {
                item.className = item.className.replace(/grid-\d+x\d+/g, '');
                item.classList.add(`grid-${layout}`);
            });

            // تحديث أحجام الأعمدة
            items.forEach(item => {
                switch(layout) {
                    case '1x1':
                        item.className = item.className.replace(/col-\w+-\d+/g, '');
                        item.classList.add('col-12');
                        break;
                    case '2x2':
                        item.className = item.className.replace(/col-\w+-\d+/g, '');
                        item.classList.add('col-lg-6', 'col-xl-6');
                        break;
                    case '3x3':
                        item.className = item.className.replace(/col-\w+-\d+/g, '');
                        item.classList.add('col-lg-4', 'col-xl-4');
                        break;
                    case '4x4':
                        item.className = item.className.replace(/col-\w+-\d+/g, '');
                        item.classList.add('col-lg-3', 'col-xl-3');
                        break;
                }
            });

            showNotification(`تم تغيير التخطيط إلى ${layout}`, 'success');
        }

        // تغيير جودة البث
        function changeQuality() {
            const quality = document.getElementById('streamQuality').value;
            showNotification(`تم تغيير جودة البث إلى ${quality}`, 'info');
            // هنا يمكن إضافة كود تغيير الجودة الفعلي
        }

        // بدء بث كاميرا واحدة
        function startStream(cameraId) {
            const videoElement = document.getElementById(`video-${cameraId}`);

            // إظهار حالة التحميل
            videoElement.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-light mb-3" role="status"></div>
                    <h5>جاري تحميل البث...</h5>
                    <p class="text-muted">الكاميرا رقم ${cameraId}</p>
                </div>
            `;

            // محاكاة تحميل البث
            setTimeout(() => {
                videoElement.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-video fa-4x mb-3 text-success"></i>
                        <h5>البث المباشر نشط</h5>
                        <p class="text-muted">الكاميرا رقم ${cameraId}</p>
                        <div class="mt-3">
                            <span class="badge bg-success">
                                <i class="fas fa-circle me-1"></i>مباشر
                            </span>
                            <span class="badge bg-info ms-2">
                                <i class="fas fa-signal me-1"></i>جودة عالية
                            </span>
                        </div>
                    </div>
                `;

                activeStreams++;
                updateStats();
                showNotification(`تم بدء البث للكاميرا رقم ${cameraId}`, 'success');
            }, 2000);
        }

        // إيقاف بث كاميرا واحدة
        function stopStream(cameraId) {
            const videoElement = document.getElementById(`video-${cameraId}`);
            const cameraName = videoElement.closest('.video-container').querySelector('.camera-info strong').textContent;

            videoElement.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-camera fa-4x mb-3"></i>
                    <h5>${cameraName}</h5>
                    <p class="text-muted">البث متوقف</p>
                    <button class="btn btn-stream" onclick="startStream(${cameraId})">
                        <i class="fas fa-play me-1"></i>بدء البث
                    </button>
                </div>
            `;

            if (activeStreams > 0) {
                activeStreams--;
                updateStats();
            }
            showNotification(`تم إيقاف البث للكاميرا رقم ${cameraId}`, 'info');
        }

        // تشغيل جميع البثوث
        function startAllStreams() {
            const cameras = document.querySelectorAll('.stream-item');
            cameras.forEach((camera, index) => {
                const cameraId = camera.getAttribute('data-camera-id');
                setTimeout(() => {
                    startStream(cameraId);
                }, index * 500);
            });
            showNotification('جاري تشغيل جميع البثوث...', 'info');
        }

        // إيقاف جميع البثوث
        function stopAllStreams() {
            const cameras = document.querySelectorAll('.stream-item');
            cameras.forEach(camera => {
                const cameraId = camera.getAttribute('data-camera-id');
                stopStream(cameraId);
            });
            activeStreams = 0;
            updateStats();
            showNotification('تم إيقاف جميع البثوث', 'info');
        }

        // تبديل ملء الشاشة
        function toggleFullscreen(cameraId) {
            const container = document.getElementById(`video-${cameraId}`).closest('.video-container');

            if (!isFullscreen) {
                container.classList.add('fullscreen');
                isFullscreen = true;
                showNotification('تم تفعيل ملء الشاشة - اضغط ESC للخروج', 'info');
            } else {
                container.classList.remove('fullscreen');
                isFullscreen = false;
                showNotification('تم إلغاء ملء الشاشة', 'info');
            }
        }

        // التقاط صورة
        function takeSnapshot(cameraId) {
            showNotification(`جاري التقاط صورة من الكاميرا رقم ${cameraId}...`, 'info');

            setTimeout(() => {
                const timestamp = new Date().toLocaleString('ar-SA');
                showNotification(`تم التقاط الصورة بنجاح - ${timestamp}`, 'success');
            }, 1500);
        }

        // تبديل التسجيل
        function toggleRecording(cameraId) {
            showNotification(`جاري تبديل حالة التسجيل للكاميرا رقم ${cameraId}...`, 'info');

            setTimeout(() => {
                const isRecording = Math.random() > 0.5;
                if (isRecording) {
                    showNotification(`تم بدء التسجيل للكاميرا رقم ${cameraId}`, 'success');
                } else {
                    showNotification(`تم إيقاف التسجيل للكاميرا رقم ${cameraId}`, 'warning');
                }
            }, 1000);
        }

        // تحديث البثوث
        function refreshStreams() {
            showNotification('جاري تحديث جميع البثوث...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('activeStreams').textContent = activeStreams;
            document.getElementById('totalBandwidth').textContent = (activeStreams * 2.5).toFixed(1) + ' MB/s';
        }

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : type === 'warning' ? 'alert-warning' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        // معالجة مفتاح ESC للخروج من ملء الشاشة
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isFullscreen) {
                const fullscreenContainer = document.querySelector('.video-container.fullscreen');
                if (fullscreenContainer) {
                    fullscreenContainer.classList.remove('fullscreen');
                    isFullscreen = false;
                    showNotification('تم إلغاء ملء الشاشة', 'info');
                }
            }
        });

        // تحديث تلقائي
        setInterval(function() {
            const autoRefresh = document.getElementById('autoRefresh').checked;
            if (autoRefresh) {
                // تحديث حالة الكاميرات
                console.log('تحديث تلقائي للبثوث...');
            }
        }, 30000);

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const containers = document.querySelectorAll('.video-container');
            containers.forEach((container, index) => {
                setTimeout(() => {
                    container.style.opacity = '0';
                    container.style.transform = 'translateY(20px)';
                    container.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        container.style.opacity = '1';
                        container.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // رسالة ترحيب
            setTimeout(() => {
                showNotification('مرحباً بك في صفحة المشاهدة المباشرة!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
    ''', online_cameras=online_cameras, session=session)

@app.route('/delete_camera/<int:camera_id>', methods=['POST'])
def delete_camera(camera_id):
    """حذف كاميرا"""
    if 'user_id' not in session or not session.get('can_control_cameras'):
        return jsonify({'success': False, 'message': 'غير مسموح لك بحذف الكاميرات'})

    try:
        conn = get_db()
        cursor = conn.cursor()

        # التحقق من وجود الكاميرا
        cursor.execute('SELECT name FROM cameras WHERE id = ?', (camera_id,))
        camera = cursor.fetchone()

        if not camera:
            return jsonify({'success': False, 'message': 'الكاميرا غير موجودة'})

        # حذف التسجيلات المرتبطة بالكاميرا (اختياري - يمكن تعديله حسب الحاجة)
        cursor.execute('DELETE FROM recordings WHERE camera_id = ?', (camera_id,))

        # حذف الكاميرا
        cursor.execute('DELETE FROM cameras WHERE id = ?', (camera_id,))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'تم حذف الكاميرا "{camera[0]}" بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حذف الكاميرا: {str(e)}'
        })

@app.route('/add_camera', methods=['GET', 'POST'])
def add_camera():
    if 'user_id' not in session or not session.get('can_control_cameras'):
        flash('غير مسموح لك بإضافة كاميرات', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # معالجة إضافة الكاميرا
        name = request.form.get('name')
        description = request.form.get('description')
        ip_address = request.form.get('ip_address')
        port = request.form.get('port', 554)
        channel = request.form.get('channel', 1)  # إضافة القناة
        username = request.form.get('username')
        password = request.form.get('password')
        camera_type = request.form.get('camera_type')
        protocol = request.form.get('protocol')
        brand = request.form.get('brand')
        model = request.form.get('model')
        custom_model = request.form.get('custom_model')
        # استخدام الموديل المخصص إذا تم اختيار "إدخال يدوي"
        if model == 'custom' and custom_model:
            model = custom_model
        location = request.form.get('location')
        motion_detection = 1 if request.form.get('motion_detection') else 0
        recording_quality = request.form.get('recording_quality')

        # إضافة الكاميرا لقاعدة البيانات
        conn = get_db()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO cameras
                (name, description, ip_address, port, channel, username, password, camera_type, protocol,
                 brand, model, location, motion_detection, recording_quality, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'offline')
            ''', (name, description, ip_address, port, channel, username, password, camera_type,
                  protocol, brand, model, location, motion_detection, recording_quality))

            conn.commit()
            flash('تم إضافة الكاميرا بنجاح!', 'success')
            return redirect(url_for('cameras'))

        except Exception as e:
            flash(f'خطأ في إضافة الكاميرا: {str(e)}', 'error')
        finally:
            conn.close()

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كاميرا جديدة - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-card {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: none;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-test {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        .btn-test:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            position: relative;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .step.completed::after {
            background: #28a745;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera me-1"></i>قائمة الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-plus-circle me-2"></i>إضافة كاميرا جديدة</h2>
                        <p class="text-muted mb-0">نموذج إضافة كاميرا جديدة مع جميع الإعدادات والخيارات المتقدمة</p>
                    </div>
                    <a href="{{ url_for('cameras') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة لقائمة الكاميرات
                    </a>
                </div>
            </div>
        </div>

        <!-- مؤشر الخطوات -->
        <div class="step-indicator">
            <div class="step active" id="step1">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="step" id="step2">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="step" id="step3">
                <i class="fas fa-cog"></i>
            </div>
            <div class="step" id="step4">
                <i class="fas fa-check"></i>
            </div>
        </div>

        <!-- رسائل التنبيه -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- نموذج إضافة الكاميرا -->
        <form method="POST" id="cameraForm">
            <div class="row">
                <div class="col-lg-8">
                    <!-- الخطوة 1: المعلومات الأساسية -->
                    <div class="card form-card mb-4" id="basicInfo">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-tag me-1"></i>اسم الكاميرا <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="name" class="form-control" required
                                           placeholder="مثال: كاميرا المدخل الرئيسي">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>الموقع
                                    </label>
                                    <input type="text" name="location" class="form-control"
                                           placeholder="مثال: المدخل الرئيسي - الطابق الأول">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-align-left me-1"></i>الوصف
                                    </label>
                                    <textarea name="description" class="form-control" rows="3"
                                              placeholder="وصف مفصل عن الكاميرا وموقعها ووظيفتها..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الخطوة 2: إعدادات الشبكة -->
                    <div class="card form-card mb-4" id="networkSettings">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-network-wired me-2"></i>إعدادات الشبكة والاتصال
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-globe me-1"></i>عنوان IP <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="ip_address" class="form-control" required
                                           placeholder="*************" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-plug me-1"></i>المنفذ (Port)
                                    </label>
                                    <input type="number" name="port" class="form-control" value="554"
                                           min="1" max="65535" placeholder="554">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-list-ol me-1"></i>القناة
                                    </label>
                                    <input type="number" name="channel" class="form-control" value="1"
                                           min="1" max="64" placeholder="1" title="رقم القناة للـ DVR/NVR">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-user me-1"></i>اسم المستخدم
                                    </label>
                                    <input type="text" name="username" class="form-control"
                                           placeholder="admin">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-lock me-1"></i>كلمة المرور
                                    </label>
                                    <div class="input-group">
                                        <input type="password" name="password" class="form-control" id="password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="toggleIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-layer-group me-1"></i>نوع الكاميرا <span class="text-danger">*</span>
                                    </label>
                                    <select name="camera_type" class="form-select" required>
                                        <option value="">اختر النوع</option>
                                        <option value="IP">كاميرا IP</option>
                                        <option value="DVR">جهاز DVR</option>
                                        <option value="NVR">جهاز NVR</option>
                                        <option value="Analog">كاميرا تناظرية</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-exchange-alt me-1"></i>البروتوكول
                                    </label>
                                    <select name="protocol" class="form-select">
                                        <option value="RTSP">RTSP</option>
                                        <option value="HTTP">HTTP</option>
                                        <option value="HTTPS">HTTPS</option>
                                        <option value="TCP">TCP</option>
                                        <option value="ONVIF">ONVIF</option>
                                    </select>
                                </div>
                            </div>

                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-test" onclick="testConnection()">
                                    <i class="fas fa-plug me-1"></i>اختبار الاتصال
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- الخطوة 3: الإعدادات المتقدمة -->
                    <div class="card form-card mb-4" id="advancedSettings">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>الإعدادات المتقدمة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-industry me-1"></i>الشركة المصنعة
                                    </label>
                                    <select name="brand" class="form-select" id="brandSelect" onchange="updateModels()">
                                        <option value="">اختر الشركة</option>
                                        <optgroup label="الشركات الرئيسية">
                                            <option value="Hikvision">Hikvision</option>
                                            <option value="Dahua">Dahua Technology</option>
                                            <option value="Uniview">Uniview</option>
                                            <option value="Axis">Axis Communications</option>
                                        </optgroup>
                                        <optgroup label="شركات أخرى">
                                            <option value="Bosch">Bosch Security</option>
                                            <option value="Samsung">Samsung Techwin</option>
                                            <option value="Sony">Sony</option>
                                            <option value="Panasonic">Panasonic</option>
                                            <option value="Honeywell">Honeywell</option>
                                            <option value="Vivotek">Vivotek</option>
                                            <option value="Hanwha">Hanwha Techwin</option>
                                            <option value="Tiandy">Tiandy</option>
                                            <option value="CP Plus">CP Plus</option>
                                            <option value="Other">أخرى</option>
                                        </optgroup>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-barcode me-1"></i>رقم الموديل
                                    </label>
                                    <select name="model" class="form-select" id="modelSelect">
                                        <option value="">اختر الموديل</option>
                                        <option value="custom">إدخال يدوي</option>
                                    </select>
                                    <input type="text" name="custom_model" class="form-control mt-2" id="customModel"
                                           placeholder="أدخل رقم الموديل يدوياً" style="display: none;">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-video me-1"></i>جودة التسجيل
                                    </label>
                                    <select name="recording_quality" class="form-select">
                                        <option value="low">منخفضة (720p)</option>
                                        <option value="medium" selected>متوسطة (1080p)</option>
                                        <option value="high">عالية (4K)</option>
                                        <option value="ultra">فائقة (8K)</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch mt-4">
                                        <input class="form-check-input" type="checkbox" name="motion_detection"
                                               id="motionDetection" checked>
                                        <label class="form-check-label" for="motionDetection">
                                            <i class="fas fa-running me-1"></i>تفعيل كشف الحركة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-lg-4">
                    <!-- معاينة الكاميرا -->
                    <div class="card form-card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>معاينة الكاميرا
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="camera-preview mb-3" style="height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                                <div>
                                    <i class="fas fa-camera fa-3x mb-2"></i>
                                    <p class="mb-0">معاينة الكاميرا</p>
                                    <small>ستظهر هنا بعد اختبار الاتصال</small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary w-100" onclick="previewCamera()">
                                <i class="fas fa-play me-1"></i>معاينة مباشرة
                            </button>
                        </div>
                    </div>

                    <!-- نصائح وإرشادات -->
                    <div class="card form-card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>نصائح وإرشادات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-1"></i>نصائح مهمة:</h6>
                                <ul class="small mb-0">
                                    <li>تأكد من أن الكاميرا متصلة بالشبكة</li>
                                    <li>استخدم عنوان IP ثابت للكاميرا</li>
                                    <li>تحقق من بيانات الدخول الصحيحة</li>
                                    <li>اختبر الاتصال قبل الحفظ</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-1"></i>المنافذ الشائعة:</h6>
                                <ul class="small mb-0">
                                    <li><strong>RTSP:</strong> 554</li>
                                    <li><strong>HTTP:</strong> 80</li>
                                    <li><strong>HTTPS:</strong> 443</li>
                                    <li><strong>ONVIF:</strong> 80, 8080</li>
                                    <li><strong>Dahua DVR:</strong> 37777</li>
                                    <li><strong>Hikvision:</strong> 8000</li>
                                </ul>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-list-ol me-1"></i>معلومات القنوات:</h6>
                                <ul class="small mb-0">
                                    <li><strong>كاميرا IP:</strong> القناة = 1</li>
                                    <li><strong>DVR/NVR:</strong> 1-64 قناة</li>
                                    <li><strong>Dahua DVR:</strong> 1-32 قناة</li>
                                    <li><strong>Hikvision NVR:</strong> 1-16 قناة</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- حالة الاتصال -->
                    <div class="card form-card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-signal me-2"></i>حالة الاتصال
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="connectionStatus" class="text-center">
                                <i class="fas fa-question-circle fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لم يتم اختبار الاتصال بعد</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card form-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">هل أنت متأكد من البيانات؟</h5>
                                    <small class="text-muted">تأكد من صحة جميع البيانات قبل إضافة الكاميرا</small>
                                </div>
                                <div>
                                    <a href="{{ url_for('cameras') }}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                    <button type="button" class="btn btn-warning me-2" onclick="resetForm()">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>إضافة الكاميرا
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // قوائم الموديلات حسب الشركة
        const brandModels = {
            'Hikvision': [
                'DS-2CD2043G0-I', 'DS-2CD2143G0-I', 'DS-2CD2343G0-I',
                'DS-2CD2T43G0-I5', 'DS-2CD2T43G0-I8', 'DS-2CD2685FWD-IZS',
                'DS-2CD2732F-I', 'DS-2CD2142FWD-I', 'DS-2CD2532F-I',
                'DS-7608NI-I2/8P', 'DS-7616NI-I2/16P', 'DS-7732NI-I4'
            ],
            'Dahua': [
                // كاميرات IP
                'IPC-HFW4431R-Z', 'IPC-HFW4831E-SE', 'IPC-HFW2431S-S-S2',
                'IPC-HDW4431C-A', 'IPC-HDW2431T-ZS', 'IPC-HDBW4431R-ZS',
                'IPC-HFW5431E-ZE', 'IPC-HFW8232E-Z', 'IPC-HFW4239T-ASE',
                // أجهزة DVR
                'XVR5108HS-4KL-X', 'XVR5116HS-4KL-X', 'XVR5132HS-4KL-X',
                'XVR4108C-4KL-X', 'XVR4116HS-X', 'XVR4132HS-X',
                'DVR5108H-4KL-PE', 'DVR5116H-4KL-PE', 'DVR5132H-4KL-PE',
                // أجهزة NVR
                'NVR4108HS-8P-4KS2', 'NVR4116HS-8P-4KS2', 'NVR4132-4KS2',
                'NVR5208-8P-4KS2E', 'NVR5216-16P-4KS2E', 'NVR5232-16P-4KS2E',
                'NVR608-32-4KS2', 'NVR608-64-4KS2', 'NVR616-128-4KS2'
            ],
            'Uniview': [
                'IPC322LR3-VSPF28-D', 'IPC2122LR3-PF28-D', 'IPC2124LR3-PF28-D',
                'IPC544E-DL-IN28', 'IPC672LR-AX4DUPK-VG', 'IPC6412LR-X22P-VG',
                'NVR301-08E2-P8', 'NVR301-16E2-P16', 'NVR302-32E-B'
            ],
            'Axis': [
                'M3046-V', 'M3047-P', 'M3048-P', 'M3049-P',
                'P1455-LE', 'P1465-LE', 'P3245-LV', 'P3248-LV',
                'Q1615 Mk III', 'Q1659', 'Q3517-LV', 'Q3518-LV'
            ]
        };

        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // تحديث قائمة الموديلات حسب الشركة
        function updateModels() {
            const brandSelect = document.getElementById('brandSelect');
            const modelSelect = document.getElementById('modelSelect');
            const customModel = document.getElementById('customModel');
            const selectedBrand = brandSelect.value;

            // مسح الخيارات الحالية
            modelSelect.innerHTML = '<option value="">اختر الموديل</option>';

            if (selectedBrand && brandModels[selectedBrand]) {
                // إضافة موديلات الشركة المختارة
                brandModels[selectedBrand].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });

                // إضافة خيار الإدخال اليدوي
                const customOption = document.createElement('option');
                customOption.value = 'custom';
                customOption.textContent = 'إدخال يدوي';
                modelSelect.appendChild(customOption);
            } else {
                // إضافة خيار الإدخال اليدوي فقط
                const customOption = document.createElement('option');
                customOption.value = 'custom';
                customOption.textContent = 'إدخال يدوي';
                modelSelect.appendChild(customOption);
            }

            // إخفاء حقل الإدخال اليدوي
            customModel.style.display = 'none';
        }

        // معالجة تغيير الموديل
        document.addEventListener('DOMContentLoaded', function() {
            const modelSelect = document.getElementById('modelSelect');
            const customModel = document.getElementById('customModel');

            modelSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customModel.style.display = 'block';
                    customModel.required = true;
                } else {
                    customModel.style.display = 'none';
                    customModel.required = false;
                    customModel.value = '';
                }
            });
        });

        // اختبار الاتصال
        function testConnection() {
            const ip = document.querySelector('input[name="ip_address"]').value;
            const port = document.querySelector('input[name="port"]').value;
            const username = document.querySelector('input[name="username"]').value;
            const password = document.querySelector('input[name="password"]').value;

            if (!ip) {
                showNotification('يرجى إدخال عنوان IP أولاً', 'error');
                return;
            }

            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = `
                <div class="spinner-border text-primary mb-2" role="status"></div>
                <p class="text-primary mb-0">جاري اختبار الاتصال...</p>
            `;

            // محاكاة اختبار الاتصال
            setTimeout(() => {
                const success = Math.random() > 0.3;
                if (success) {
                    statusDiv.innerHTML = `
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-success mb-0">الاتصال ناجح!</p>
                        <small class="text-muted">الكاميرا متاحة ويمكن الوصول إليها</small>
                    `;
                    showNotification('تم الاتصال بالكاميرا بنجاح!', 'success');
                } else {
                    statusDiv.innerHTML = `
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <p class="text-danger mb-0">فشل الاتصال</p>
                        <small class="text-muted">تحقق من عنوان IP وبيانات الدخول</small>
                    `;
                    showNotification('فشل الاتصال بالكاميرا. تحقق من البيانات.', 'error');
                }
            }, 3000);
        }

        // معاينة الكاميرا
        function previewCamera() {
            showNotification('جاري تحميل المعاينة المباشرة...', 'info');
            // هنا يمكن إضافة كود المعاينة المباشرة
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                document.getElementById('cameraForm').reset();
                document.getElementById('connectionStatus').innerHTML = `
                    <i class="fas fa-question-circle fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لم يتم اختبار الاتصال بعد</p>
                `;
                showNotification('تم إعادة تعيين النموذج', 'info');
            }
        }

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.form-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });

        // التحقق من صحة النموذج
        document.getElementById('cameraForm').addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value;
            const ip = document.querySelector('input[name="ip_address"]').value;
            const type = document.querySelector('select[name="camera_type"]').value;

            if (!name || !ip || !type) {
                e.preventDefault();
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            showNotification('جاري إضافة الكاميرا...', 'info');
        });
    </script>
</body>
</html>
    ''', session=session)

@app.route('/recordings')
def recordings():
    if 'user_id' not in session or not session.get('can_manage_recordings'):
        flash('غير مسموح لك بعرض التسجيلات', 'error')
        return redirect(url_for('dashboard'))

    # الحصول على جميع التسجيلات مع معلومات الكاميرات
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT r.id, r.filename, r.file_path, r.file_size, r.duration, r.recording_type,
               r.quality, r.start_time, r.end_time, r.status, r.is_protected, r.created_at,
               c.name as camera_name, c.location as camera_location, c.brand, c.model
        FROM recordings r
        JOIN cameras c ON r.camera_id = c.id
        ORDER BY r.start_time DESC
    ''')
    all_recordings = cursor.fetchall()

    # إحصائيات التسجيلات
    cursor.execute('SELECT COUNT(*) FROM recordings')
    total_recordings = cursor.fetchone()[0]

    cursor.execute('SELECT SUM(file_size) FROM recordings')
    total_size = cursor.fetchone()[0] or 0

    cursor.execute('SELECT SUM(duration) FROM recordings')
    total_duration = cursor.fetchone()[0] or 0

    cursor.execute('SELECT COUNT(*) FROM recordings WHERE is_protected = 1')
    protected_recordings = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM recordings WHERE DATE(start_time) = DATE("now")')
    today_recordings = cursor.fetchone()[0]

    conn.close()

    stats = {
        'total': total_recordings,
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'total_hours': round(total_duration / 3600, 1),
        'protected': protected_recordings,
        'today': today_recordings
    }

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع التسجيلات - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            transition: transform 0.3s;
        }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-card.bg-success { background: linear-gradient(135deg, #28a745, #20c997) !important; }
        .stats-card.bg-warning { background: linear-gradient(135deg, #ffc107, #fd7e14) !important; }
        .stats-card.bg-info { background: linear-gradient(135deg, #17a2b8, #6f42c1) !important; }
        .stats-card.bg-danger { background: linear-gradient(135deg, #dc3545, #fd7e14) !important; }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .recording-card {
            border-radius: 10px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .recording-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .recording-thumbnail {
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .recording-info {
            flex-grow: 1;
            padding: 0 15px;
        }

        .recording-actions {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .duration-badge {
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
            position: absolute;
            bottom: 5px;
            right: 5px;
        }

        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera me-1"></i>الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-video me-2"></i>جميع التسجيلات</h2>
                        <p class="text-muted mb-0">قائمة مفصلة بجميع التسجيلات مع إمكانيات البحث والفلترة المتقدمة</p>
                    </div>
                    <div>
                        <a href="{{ url_for('recordings_search') }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search me-1"></i>البحث المتقدم
                        </a>
                        <button class="btn btn-outline-success" onclick="refreshRecordings()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات التسجيلات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.total }}</h3>
                            <p class="mb-0">إجمالي التسجيلات</p>
                            <small class="opacity-75">{{ stats.today }} اليوم</small>
                        </div>
                        <i class="fas fa-video fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-success p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.total_hours }}</h3>
                            <p class="mb-0">ساعات التسجيل</p>
                            <small class="opacity-75">إجمالي المدة</small>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-warning p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.total_size_mb }}</h3>
                            <p class="mb-0">ميجابايت</p>
                            <small class="opacity-75">مساحة التخزين</small>
                        </div>
                        <i class="fas fa-hdd fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stats-card bg-danger p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.protected }}</h3>
                            <p class="mb-0">تسجيلات محمية</p>
                            <small class="opacity-75">لا يمكن حذفها</small>
                        </div>
                        <i class="fas fa-shield-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والفلترة -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control search-box" placeholder="البحث في التسجيلات..." id="searchInput" onkeyup="searchRecordings()">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="typeFilter" onchange="filterRecordings()">
                        <option value="">جميع الأنواع</option>
                        <option value="manual">يدوي</option>
                        <option value="scheduled">مجدول</option>
                        <option value="motion">كشف حركة</option>
                        <option value="alarm">إنذار</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="qualityFilter" onchange="filterRecordings()">
                        <option value="">جميع الجودات</option>
                        <option value="low">منخفضة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">عالية</option>
                        <option value="ultra">فائقة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter" onchange="filterRecordings()">
                        <option value="">جميع الحالات</option>
                        <option value="completed">مكتمل</option>
                        <option value="processing">قيد المعالجة</option>
                        <option value="failed">فاشل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="protectedFilter" onchange="filterRecordings()">
                        <option value="">الكل</option>
                        <option value="protected">محمي</option>
                        <option value="unprotected">غير محمي</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول التسجيلات -->
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="15%">معاينة</th>
                                <th width="20%">اسم الملف</th>
                                <th width="15%">الكاميرا</th>
                                <th width="10%">النوع</th>
                                <th width="8%">المدة</th>
                                <th width="8%">الحجم</th>
                                <th width="12%">التاريخ</th>
                                <th width="7%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recordingsTable">
                            {% if all_recordings %}
                            {% for recording in all_recordings %}
                            <tr class="recording-row"
                                data-filename="{{ recording[1] }}"
                                data-type="{{ recording[5] }}"
                                data-quality="{{ recording[6] }}"
                                data-status="{{ recording[9] }}"
                                data-protected="{{ 'protected' if recording[10] else 'unprotected' }}">
                                <td>
                                    <input type="checkbox" class="recording-checkbox" value="{{ recording[0] }}">
                                </td>
                                <td>
                                    <div class="recording-thumbnail position-relative">
                                        <i class="fas fa-play"></i>
                                        <div class="duration-badge">{{ recording[4] | format_duration }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ recording[1] }}</strong>
                                        {% if recording[10] %}
                                        <i class="fas fa-shield-alt text-warning ms-1" title="محمي"></i>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ recording[2] }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ recording[12] }}</strong><br>
                                        <small class="text-muted">{{ recording[13] or 'غير محدد' }}</small>
                                        {% if recording[14] and recording[15] %}
                                        <br><small class="text-muted">{{ recording[14] }} {{ recording[15] }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if recording[5] == 'manual' else 'success' if recording[5] == 'scheduled' else 'warning' if recording[5] == 'motion' else 'danger' }}">
                                        {% if recording[5] == 'manual' %}
                                        <i class="fas fa-hand-paper me-1"></i>يدوي
                                        {% elif recording[5] == 'scheduled' %}
                                        <i class="fas fa-clock me-1"></i>مجدول
                                        {% elif recording[5] == 'motion' %}
                                        <i class="fas fa-running me-1"></i>كشف حركة
                                        {% elif recording[5] == 'alarm' %}
                                        <i class="fas fa-exclamation-triangle me-1"></i>إنذار
                                        {% else %}
                                        {{ recording[5] }}
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">{{ recording[6] }}</small>
                                </td>
                                <td>
                                    <strong>{{ recording[4] | format_duration }}</strong>
                                </td>
                                <td>
                                    <strong>{{ recording[3] | format_file_size }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ recording[7] }}</strong><br>
                                        {% if recording[8] %}
                                        <small class="text-muted">إلى: {{ recording[8] }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="playRecording({{ recording[0] }})" title="تشغيل">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="downloadRecording({{ recording[0] }})" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="toggleProtection({{ recording[0] }}, {{ recording[10] }})" title="{{ 'إلغاء الحماية' if recording[10] else 'حماية' }}">
                                            <i class="fas fa-{{ 'shield-alt' if not recording[10] else 'unlock' }}"></i>
                                        </button>
                                        {% if not recording[10] %}
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteRecording({{ recording[0] }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد تسجيلات</h5>
                                    <p class="text-muted">لم يتم العثور على أي تسجيلات في النظام</p>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- أدوات الإجراءات المجمعة -->
        {% if all_recordings %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="filter-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span id="selectedCount">0</span> تسجيل محدد
                        </div>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="downloadSelected()" disabled id="downloadBtn">
                                <i class="fas fa-download me-1"></i>تحميل المحدد
                            </button>
                            <button class="btn btn-outline-warning me-2" onclick="protectSelected()" disabled id="protectBtn">
                                <i class="fas fa-shield-alt me-1"></i>حماية المحدد
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSelected()" disabled id="deleteBtn">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- رسالة عدم وجود نتائج -->
        <div id="noResults" class="text-center py-5" style="display: none;">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد نتائج</h4>
            <p class="text-muted">لم يتم العثور على تسجيلات تطابق معايير البحث</p>
            <button class="btn btn-outline-primary" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>مسح الفلاتر
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedRecordings = [];

        // البحث في التسجيلات
        function searchRecordings() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('.recording-row');
            let visibleCount = 0;

            rows.forEach(row => {
                const filename = row.getAttribute('data-filename').toLowerCase();
                if (filename.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            toggleNoResults(visibleCount === 0);
        }

        // فلترة التسجيلات
        function filterRecordings() {
            const typeFilter = document.getElementById('typeFilter').value;
            const qualityFilter = document.getElementById('qualityFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const protectedFilter = document.getElementById('protectedFilter').value;
            const rows = document.querySelectorAll('.recording-row');
            let visibleCount = 0;

            rows.forEach(row => {
                let show = true;

                if (typeFilter && row.getAttribute('data-type') !== typeFilter) {
                    show = false;
                }
                if (qualityFilter && row.getAttribute('data-quality') !== qualityFilter) {
                    show = false;
                }
                if (statusFilter && row.getAttribute('data-status') !== statusFilter) {
                    show = false;
                }
                if (protectedFilter && row.getAttribute('data-protected') !== protectedFilter) {
                    show = false;
                }

                if (show) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            toggleNoResults(visibleCount === 0);
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('qualityFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('protectedFilter').value = '';

            const rows = document.querySelectorAll('.recording-row');
            rows.forEach(row => {
                row.style.display = '';
            });

            toggleNoResults(false);
        }

        // إظهار/إخفاء رسالة عدم وجود نتائج
        function toggleNoResults(show) {
            const noResults = document.getElementById('noResults');
            const table = document.querySelector('.table-responsive');

            if (show) {
                noResults.style.display = 'block';
                table.style.display = 'none';
            } else {
                noResults.style.display = 'none';
                table.style.display = 'block';
            }
        }

        // تحديث التسجيلات
        function refreshRecordings() {
            showNotification('جاري تحديث قائمة التسجيلات...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // تشغيل التسجيل
        function playRecording(recordingId) {
            showNotification('جاري تحميل مشغل الفيديو...', 'info');

            // محاكاة فتح مشغل الفيديو
            setTimeout(() => {
                showNotification('تم فتح مشغل الفيديو للتسجيل رقم: ' + recordingId, 'success');
            }, 2000);
        }

        // تحميل التسجيل
        function downloadRecording(recordingId) {
            showNotification('جاري بدء تحميل التسجيل رقم: ' + recordingId, 'info');

            // محاكاة التحميل
            setTimeout(() => {
                showNotification('تم بدء التحميل بنجاح!', 'success');
            }, 1500);
        }

        // تبديل حماية التسجيل
        function toggleProtection(recordingId, isProtected) {
            const action = isProtected ? 'إلغاء حماية' : 'حماية';
            showNotification('جاري ' + action + ' التسجيل رقم: ' + recordingId, 'info');

            setTimeout(() => {
                showNotification('تم ' + action + ' التسجيل بنجاح!', 'success');
                // هنا يمكن تحديث الواجهة
            }, 1000);
        }

        // حذف التسجيل
        function deleteRecording(recordingId) {
            if (confirm('هل أنت متأكد من حذف هذا التسجيل؟\\nلا يمكن التراجع عن هذا الإجراء.')) {
                showNotification('جاري حذف التسجيل رقم: ' + recordingId, 'warning');

                setTimeout(() => {
                    showNotification('تم حذف التسجيل بنجاح!', 'success');
                    // إزالة الصف من الجدول
                    const row = document.querySelector(`input[value="${recordingId}"]`).closest('tr');
                    row.remove();
                }, 1500);
            }
        }

        // تحديد/إلغاء تحديد الكل
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.recording-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedCount();
        }

        // تحديث عدد المحدد
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.recording-checkbox:checked');
            const count = checkboxes.length;

            document.getElementById('selectedCount').textContent = count;

            // تفعيل/إلغاء تفعيل الأزرار
            const downloadBtn = document.getElementById('downloadBtn');
            const protectBtn = document.getElementById('protectBtn');
            const deleteBtn = document.getElementById('deleteBtn');

            if (downloadBtn) downloadBtn.disabled = count === 0;
            if (protectBtn) protectBtn.disabled = count === 0;
            if (deleteBtn) deleteBtn.disabled = count === 0;

            selectedRecordings = Array.from(checkboxes).map(cb => cb.value);
        }

        // تحميل المحدد
        function downloadSelected() {
            if (selectedRecordings.length === 0) return;

            showNotification(`جاري تحميل ${selectedRecordings.length} تسجيل...`, 'info');

            setTimeout(() => {
                showNotification('تم بدء تحميل جميع التسجيلات المحددة!', 'success');
            }, 2000);
        }

        // حماية المحدد
        function protectSelected() {
            if (selectedRecordings.length === 0) return;

            showNotification(`جاري حماية ${selectedRecordings.length} تسجيل...`, 'info');

            setTimeout(() => {
                showNotification('تم حماية جميع التسجيلات المحددة!', 'success');
            }, 1500);
        }

        // حذف المحدد
        function deleteSelected() {
            if (selectedRecordings.length === 0) return;

            if (confirm(`هل أنت متأكد من حذف ${selectedRecordings.length} تسجيل؟\\nلا يمكن التراجع عن هذا الإجراء.`)) {
                showNotification(`جاري حذف ${selectedRecordings.length} تسجيل...`, 'warning');

                setTimeout(() => {
                    showNotification('تم حذف جميع التسجيلات المحددة!', 'success');
                    // إزالة الصفوف من الجدول
                    selectedRecordings.forEach(id => {
                        const row = document.querySelector(`input[value="${id}"]`).closest('tr');
                        row.remove();
                    });
                    updateSelectedCount();
                }, 2000);
            }
        }

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : type === 'warning' ? 'alert-warning' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع لتغيير حالة الـ checkboxes
            const checkboxes = document.querySelectorAll('.recording-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCount);
            });

            // تأثيرات بصرية عند التحميل
            const cards = document.querySelectorAll('.stats-card, .filter-section, .table-responsive');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
    ''', all_recordings=all_recordings, stats=stats, session=session)

@app.route('/recordings_search', methods=['GET', 'POST'])
def recordings_search():
    if 'user_id' not in session or not session.get('can_manage_recordings'):
        flash('غير مسموح لك بالبحث في التسجيلات', 'error')
        return redirect(url_for('dashboard'))

    search_results = []
    search_performed = False

    if request.method == 'POST':
        search_performed = True

        # معايير البحث
        filename = request.form.get('filename', '').strip()
        camera_id = request.form.get('camera_id')
        recording_type = request.form.get('recording_type')
        quality = request.form.get('quality')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        min_duration = request.form.get('min_duration')
        max_duration = request.form.get('max_duration')
        min_size = request.form.get('min_size')
        max_size = request.form.get('max_size')
        is_protected = request.form.get('is_protected')

        # بناء استعلام البحث
        conn = get_db()
        cursor = conn.cursor()

        query = '''
            SELECT r.id, r.filename, r.file_path, r.file_size, r.duration, r.recording_type,
                   r.quality, r.start_time, r.end_time, r.status, r.is_protected, r.created_at,
                   c.name as camera_name, c.location as camera_location, c.brand, c.model
            FROM recordings r
            JOIN cameras c ON r.camera_id = c.id
            WHERE 1=1
        '''
        params = []

        if filename:
            query += ' AND r.filename LIKE ?'
            params.append(f'%{filename}%')

        if camera_id:
            query += ' AND r.camera_id = ?'
            params.append(camera_id)

        if recording_type:
            query += ' AND r.recording_type = ?'
            params.append(recording_type)

        if quality:
            query += ' AND r.quality = ?'
            params.append(quality)

        if start_date:
            query += ' AND DATE(r.start_time) >= ?'
            params.append(start_date)

        if end_date:
            query += ' AND DATE(r.start_time) <= ?'
            params.append(end_date)

        if min_duration:
            query += ' AND r.duration >= ?'
            params.append(int(min_duration) * 60)  # تحويل إلى ثوان

        if max_duration:
            query += ' AND r.duration <= ?'
            params.append(int(max_duration) * 60)  # تحويل إلى ثوان

        if min_size:
            query += ' AND r.file_size >= ?'
            params.append(int(min_size) * 1024 * 1024)  # تحويل إلى بايت

        if max_size:
            query += ' AND r.file_size <= ?'
            params.append(int(max_size) * 1024 * 1024)  # تحويل إلى بايت

        if is_protected:
            query += ' AND r.is_protected = ?'
            params.append(1 if is_protected == 'yes' else 0)

        query += ' ORDER BY r.start_time DESC LIMIT 100'

        cursor.execute(query, params)
        search_results = cursor.fetchall()
        conn.close()

    # الحصول على قائمة الكاميرات للفلتر
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT id, name FROM cameras ORDER BY name')
    cameras = cursor.fetchall()
    conn.close()

    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث في التسجيلات - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-card {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 20px;
        }
        .search-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-search {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
        }
        .btn-search:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
            transform: translateY(-2px);
        }
        .results-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .result-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .result-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }
        .recording-thumbnail {
            width: 100px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        .advanced-options {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة المتقدم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('recordings') }}">
                    <i class="fas fa-video me-1"></i>جميع التسجيلات
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-search me-2"></i>البحث المتقدم في التسجيلات</h2>
                        <p class="text-muted mb-0">أدوات بحث متقدمة للعثور على التسجيلات بمعايير مفصلة</p>
                    </div>
                    <a href="{{ url_for('recordings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة لجميع التسجيلات
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج البحث -->
        <div class="card search-card">
            <div class="search-section">
                <h4><i class="fas fa-filter me-2"></i>معايير البحث</h4>
                <p class="mb-0">استخدم المعايير التالية للبحث في التسجيلات</p>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- البحث الأساسي -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-file-video me-1"></i>اسم الملف
                            </label>
                            <input type="text" name="filename" class="form-control"
                                   placeholder="البحث في أسماء الملفات..."
                                   value="{{ request.form.get('filename', '') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-camera me-1"></i>الكاميرا
                            </label>
                            <select name="camera_id" class="form-select">
                                <option value="">جميع الكاميرات</option>
                                {% for camera in cameras %}
                                <option value="{{ camera[0] }}" {{ 'selected' if request.form.get('camera_id') == camera[0]|string }}>
                                    {{ camera[1] }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-tag me-1"></i>نوع التسجيل
                            </label>
                            <select name="recording_type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="manual" {{ 'selected' if request.form.get('recording_type') == 'manual' }}>يدوي</option>
                                <option value="scheduled" {{ 'selected' if request.form.get('recording_type') == 'scheduled' }}>مجدول</option>
                                <option value="motion" {{ 'selected' if request.form.get('recording_type') == 'motion' }}>كشف حركة</option>
                                <option value="alarm" {{ 'selected' if request.form.get('recording_type') == 'alarm' }}>إنذار</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-video me-1"></i>جودة التسجيل
                            </label>
                            <select name="quality" class="form-select">
                                <option value="">جميع الجودات</option>
                                <option value="low" {{ 'selected' if request.form.get('quality') == 'low' }}>منخفضة</option>
                                <option value="medium" {{ 'selected' if request.form.get('quality') == 'medium' }}>متوسطة</option>
                                <option value="high" {{ 'selected' if request.form.get('quality') == 'high' }}>عالية</option>
                                <option value="ultra" {{ 'selected' if request.form.get('quality') == 'ultra' }}>فائقة</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>الحماية
                            </label>
                            <select name="is_protected" class="form-select">
                                <option value="">الكل</option>
                                <option value="yes" {{ 'selected' if request.form.get('is_protected') == 'yes' }}>محمي</option>
                                <option value="no" {{ 'selected' if request.form.get('is_protected') == 'no' }}>غير محمي</option>
                            </select>
                        </div>
                    </div>

                    <!-- خيارات متقدمة -->
                    <div class="advanced-options">
                        <h6><i class="fas fa-cogs me-2"></i>خيارات متقدمة</h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-1"></i>من تاريخ
                                </label>
                                <input type="date" name="start_date" class="form-control"
                                       value="{{ request.form.get('start_date', '') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-1"></i>إلى تاريخ
                                </label>
                                <input type="date" name="end_date" class="form-control"
                                       value="{{ request.form.get('end_date', '') }}">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-clock me-1"></i>أقل مدة (دقيقة)
                                </label>
                                <input type="number" name="min_duration" class="form-control"
                                       placeholder="0" min="0" value="{{ request.form.get('min_duration', '') }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-clock me-1"></i>أكبر مدة (دقيقة)
                                </label>
                                <input type="number" name="max_duration" class="form-control"
                                       placeholder="∞" min="0" value="{{ request.form.get('max_duration', '') }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-hdd me-1"></i>أقل حجم (MB)
                                </label>
                                <input type="number" name="min_size" class="form-control"
                                       placeholder="0" min="0" value="{{ request.form.get('min_size', '') }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-hdd me-1"></i>أكبر حجم (MB)
                                </label>
                                <input type="number" name="max_size" class="form-control"
                                       placeholder="∞" min="0" value="{{ request.form.get('max_size', '') }}">
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-search me-3">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                            <i class="fas fa-times me-2"></i>مسح
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- نتائج البحث -->
        {% if search_performed %}
        <div class="results-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>
                    <i class="fas fa-list me-2"></i>نتائج البحث
                    <span class="badge bg-primary">{{ search_results|length }} نتيجة</span>
                </h4>
                {% if search_results %}
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="selectAllResults()">
                        <i class="fas fa-check-square me-1"></i>تحديد الكل
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="downloadSelected()">
                        <i class="fas fa-download me-1"></i>تحميل المحدد
                    </button>
                </div>
                {% endif %}
            </div>

            {% if search_results %}
            <div class="row">
                {% for recording in search_results %}
                <div class="col-12">
                    <div class="result-item">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <input type="checkbox" class="form-check-input result-checkbox" value="{{ recording[0] }}">
                            </div>
                            <div class="col-md-2">
                                <div class="recording-thumbnail position-relative">
                                    <i class="fas fa-play"></i>
                                    <div style="position: absolute; bottom: 5px; right: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.8rem;">
                                        {{ recording[4] | format_duration }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div>
                                    <h6 class="mb-1">
                                        {{ recording[1] }}
                                        {% if recording[10] %}
                                        <i class="fas fa-shield-alt text-warning ms-1" title="محمي"></i>
                                        {% endif %}
                                    </h6>
                                    <p class="text-muted small mb-1">{{ recording[12] }} - {{ recording[13] or 'غير محدد' }}</p>
                                    <p class="text-muted small mb-0">{{ recording[2] }}</p>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <span class="badge bg-{{ 'primary' if recording[5] == 'manual' else 'success' if recording[5] == 'scheduled' else 'warning' if recording[5] == 'motion' else 'danger' }} mb-1">
                                        {% if recording[5] == 'manual' %}يدوي
                                        {% elif recording[5] == 'scheduled' %}مجدول
                                        {% elif recording[5] == 'motion' %}كشف حركة
                                        {% elif recording[5] == 'alarm' %}إنذار
                                        {% else %}{{ recording[5] }}
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">{{ recording[6] }}</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <strong>{{ recording[3] | format_file_size }}</strong><br>
                                    <small class="text-muted">{{ recording[7] }}</small>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" onclick="playRecording({{ recording[0] }})" title="تشغيل">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadRecording({{ recording[0] }})" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">لم يتم العثور على تسجيلات تطابق معايير البحث المحددة</p>
                <button class="btn btn-outline-primary" onclick="clearForm()">
                    <i class="fas fa-edit me-1"></i>تعديل معايير البحث
                </button>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- نصائح البحث -->
        {% if not search_performed %}
        <div class="results-section">
            <h4><i class="fas fa-lightbulb me-2"></i>نصائح للبحث الفعال</h4>

            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>نصائح عامة:</h6>
                        <ul class="small mb-0">
                            <li>استخدم جزء من اسم الملف للبحث السريع</li>
                            <li>حدد كاميرا معينة لتضييق النتائج</li>
                            <li>استخدم نوع التسجيل للعثور على تسجيلات محددة</li>
                            <li>اتركالحقول فارغة للبحث في جميع التسجيلات</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock me-1"></i>البحث بالتاريخ:</h6>
                        <ul class="small mb-0">
                            <li>استخدم "من تاريخ" للبحث من تاريخ معين</li>
                            <li>استخدم "إلى تاريخ" للبحث حتى تاريخ معين</li>
                            <li>يمكن استخدام تاريخ واحد فقط</li>
                            <li>التاريخ يشمل اليوم كاملاً</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-hdd me-1"></i>البحث بالحجم والمدة:</h6>
                        <ul class="small mb-0">
                            <li>المدة بالدقائق (مثال: 30 دقيقة)</li>
                            <li>الحجم بالميجابايت (مثال: 100 MB)</li>
                            <li>يمكن تحديد حد أدنى وأقصى</li>
                            <li>اترك فارغاً لعدم التحديد</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-primary">
                        <h6><i class="fas fa-filter me-1"></i>أمثلة على البحث:</h6>
                        <ul class="small mb-0">
                            <li><strong>تسجيلات اليوم:</strong> حدد تاريخ اليوم في "من تاريخ"</li>
                            <li><strong>تسجيلات كشف الحركة:</strong> اختر "كشف حركة" في النوع</li>
                            <li><strong>تسجيلات كبيرة:</strong> حدد حجم أكبر من 100 MB</li>
                            <li><strong>تسجيلات طويلة:</strong> حدد مدة أكبر من 60 دقيقة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // مسح النموذج
        function clearForm() {
            document.querySelector('form').reset();
            showNotification('تم مسح جميع معايير البحث', 'info');
        }

        // تحديد جميع النتائج
        function selectAllResults() {
            const checkboxes = document.querySelectorAll('.result-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
            });

            showNotification(allChecked ? 'تم إلغاء تحديد جميع النتائج' : 'تم تحديد جميع النتائج', 'info');
        }

        // تحميل المحدد
        function downloadSelected() {
            const selected = document.querySelectorAll('.result-checkbox:checked');
            if (selected.length === 0) {
                showNotification('يرجى تحديد تسجيل واحد على الأقل', 'warning');
                return;
            }

            showNotification(`جاري تحميل ${selected.length} تسجيل...`, 'info');

            setTimeout(() => {
                showNotification('تم بدء تحميل جميع التسجيلات المحددة!', 'success');
            }, 2000);
        }

        // تشغيل التسجيل
        function playRecording(recordingId) {
            showNotification('جاري تحميل مشغل الفيديو...', 'info');

            setTimeout(() => {
                showNotification('تم فتح مشغل الفيديو للتسجيل رقم: ' + recordingId, 'success');
            }, 2000);
        }

        // تحميل التسجيل
        function downloadRecording(recordingId) {
            showNotification('جاري بدء تحميل التسجيل رقم: ' + recordingId, 'info');

            setTimeout(() => {
                showNotification('تم بدء التحميل بنجاح!', 'success');
            }, 1500);
        }

        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : type === 'warning' ? 'alert-warning' : 'alert-info';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.search-card, .results-section');
            elements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';
                    element.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 300);
            });
        });
    </script>
</body>
</html>
    ''', search_results=search_results, search_performed=search_performed, cameras=cameras, session=session, request=request)

@app.route('/alerts')
def alerts():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    alerts = get_alerts()
    return render_template_string('''
    <h1>جميع التنبيهات</h1>
    <p>هنا ستظهر قائمة مفصلة بجميع التنبيهات مع إمكانيات الإدارة</p>
    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    ''')

@app.route('/users')
def users():
    if 'user_id' not in session or not session.get('is_admin'):
        flash('غير مسموح لك بإدارة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    return render_template_string('''
    <h1>إدارة المستخدمين</h1>
    <p>هنا ستظهر قائمة المستخدمين مع إمكانيات الإدارة الكاملة</p>
    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    ''')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

# API للإحصائيات
@app.route('/api/stats')
def api_stats():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مسموح'}), 401

    stats = get_stats()
    return jsonify(stats)

# API لحالة الكاميرات
@app.route('/api/cameras/status')
def api_cameras_status():
    if 'user_id' not in session or not session.get('can_view_cameras'):
        return jsonify({'error': 'غير مسموح'}), 401

    cameras = get_cameras()
    camera_status = []

    for camera in cameras:
        camera_status.append({
            'id': camera[0],
            'name': camera[1],
            'status': camera[10],
            'is_recording': camera[11],
            'motion_detection': camera[12]
        })

    return jsonify(camera_status)

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    init_db()

    print("🚀 نظام مراقبة الكاميرات المتقدم")
    print("=" * 60)
    print("✅ تم تهيئة قاعدة البيانات المتقدمة")
    print("✅ تم إنشاء المستخدم الافتراضي")
    print("✅ تم إضافة بيانات تجريبية شاملة")
    print("=" * 60)
    print("🎉 المميزات المتوفرة:")
    print("📹 • دعم جميع أنواع الكاميرات (IP, DVR, NVR)")
    print("🎥 • بث مباشر عالي الجودة")
    print("📼 • تسجيل تلقائي ومجدول")
    print("🔍 • كشف الحركة المتقدم")
    print("🔔 • نظام تنبيهات شامل")
    print("👥 • إدارة المستخدمين والصلاحيات")
    print("📊 • إحصائيات وتقارير مفصلة")
    print("🌐 • واجهة عربية متطورة")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("📋 البيانات التجريبية تشمل:")
    print("   • 5 كاميرات متنوعة")
    print("   • 4 تسجيلات تجريبية")
    print("   • 5 تنبيهات متنوعة")
    print("   • إحصائيات شاملة")
    print("=" * 60)
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)

    app.run(host='0.0.0.0', port=5000, debug=True)
