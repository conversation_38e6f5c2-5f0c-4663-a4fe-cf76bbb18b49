{% extends "base.html" %}
{% set title = "إدارة الكاميرات" %}

{% block content %}
<div class="container-fluid">
    
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-gradient mb-1">
                        <i class="fas fa-camera me-2"></i>
                        إدارة الكاميرات
                    </h2>
                    <p class="text-muted mb-0">عرض وإدارة جميع الكاميرات في النظام</p>
                </div>
                <div>
                    {% if current_user.has_permission('can_manage_cameras') %}
                    <a href="{{ url_for('cameras.add') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة كاميرا جديدة
                    </a>
                    {% endif %}
                    <button class="btn btn-outline-secondary" onclick="refreshCameraList()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات الكاميرات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.total }}</h3>
                            <p class="mb-0">إجمالي الكاميرات</p>
                        </div>
                        <i class="fas fa-camera fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.online }}</h3>
                            <p class="mb-0">متصلة</p>
                        </div>
                        <i class="fas fa-wifi fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.offline }}</h3>
                            <p class="mb-0">غير متصلة</p>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ stats.recording }}</h3>
                            <p class="mb-0">تسجل الآن</p>
                        </div>
                        <i class="fas fa-record-vinyl fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- فلاتر البحث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="ابحث بالاسم أو الموقع أو IP..."
                                   value="{{ current_filters.search or '' }}">
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">نوع الكاميرا</label>
                            <select name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="IP" {{ 'selected' if current_filters.type == 'IP' }}>كاميرا IP</option>
                                <option value="DVR" {{ 'selected' if current_filters.type == 'DVR' }}>جهاز DVR</option>
                                <option value="NVR" {{ 'selected' if current_filters.type == 'NVR' }}>جهاز NVR</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="online" {{ 'selected' if current_filters.status == 'online' }}>متصل</option>
                                <option value="offline" {{ 'selected' if current_filters.status == 'offline' }}>غير متصل</option>
                                <option value="recording" {{ 'selected' if current_filters.status == 'recording' }}>يسجل</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول الكاميرات -->
    <div class="row">
        <div class="col-12">
            <div class="card camera-table">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الكاميرات
                    </h5>
                    {% if current_user.has_permission('can_manage_cameras') %}
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="selectAllCameras()">
                            <i class="fas fa-check-square me-1"></i>تحديد الكل
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="bulkActions()">
                            <i class="fas fa-cogs me-1"></i>عمليات مجمعة
                        </button>
                    </div>
                    {% endif %}
                </div>
                
                <div class="card-body p-0">
                    {% if cameras %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    {% if current_user.has_permission('can_manage_cameras') %}
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    {% endif %}
                                    <th>الكاميرا</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>الموقع</th>
                                    <th>آخر اتصال</th>
                                    <th width="200">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for camera in cameras %}
                                <tr data-camera-id="{{ camera.id }}">
                                    {% if current_user.has_permission('can_manage_cameras') %}
                                    <td>
                                        <input type="checkbox" class="form-check-input camera-checkbox" 
                                               value="{{ camera.id }}">
                                    </td>
                                    {% endif %}
                                    
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="camera-preview me-3">
                                                {% if camera.is_online() %}
                                                <img src="{{ url_for('streaming.snapshot', camera_id=camera.id) }}" 
                                                     class="rounded" width="60" height="45" 
                                                     alt="معاينة الكاميرا" 
                                                     onerror="this.src='/static/images/camera-offline.png'">
                                                {% else %}
                                                <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                                     style="width: 60px; height: 45px;">
                                                    <i class="fas fa-camera text-white"></i>
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ camera.name }}</div>
                                                <small class="text-muted">{{ camera.ip_address }}:{{ camera.port }}</small>
                                                {% if camera.brand %}
                                                <div><small class="text-muted">{{ camera.brand }} {{ camera.model or '' }}</small></div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    
                                    <td>
                                        <span class="badge bg-secondary">{{ camera.camera_type }}</span>
                                        <div><small class="text-muted">{{ camera.protocol }}</small></div>
                                    </td>
                                    
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="camera-status {{ 'online' if camera.is_online() else 'offline' }} mb-1">
                                                <i class="fas fa-circle me-1"></i>
                                                {{ 'متصل' if camera.is_online() else 'غير متصل' }}
                                            </span>
                                            {% if camera.is_recording %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-record-vinyl me-1"></i>يسجل
                                            </span>
                                            {% endif %}
                                            {% if camera.motion_detection %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-running me-1"></i>كشف حركة
                                            </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <td>
                                        {% if camera.location %}
                                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                                        {{ camera.location }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    
                                    <td>
                                        {% if camera.last_seen %}
                                        <small class="text-muted">
                                            {{ camera.last_seen.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                        {% else %}
                                        <span class="text-muted">لم يتصل بعد</span>
                                        {% endif %}
                                    </td>
                                    
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('cameras.view', id=camera.id) }}" 
                                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            {% if camera.is_online() %}
                                            <a href="{{ url_for('streaming.live_view', camera_id=camera.id) }}" 
                                               class="btn btn-outline-success" title="مشاهدة مباشرة">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            {% endif %}
                                            
                                            {% if current_user.has_permission('can_manage_cameras') %}
                                            <a href="{{ url_for('cameras.edit', id=camera.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteCamera({{ camera.id }}, '{{ camera.name }}')" 
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- تقسيم الصفحات -->
                    {% if pagination.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="تقسيم الصفحات">
                            <ul class="pagination justify-content-center mb-0">
                                {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('cameras.index', page=pagination.prev_num, **current_filters) }}">
                                        السابق
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for page_num in pagination.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != pagination.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('cameras.index', page=page_num, **current_filters) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('cameras.index', page=pagination.next_num, **current_filters) }}">
                                        التالي
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <!-- رسالة عدم وجود كاميرات -->
                    <div class="text-center py-5">
                        <i class="fas fa-camera fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد كاميرات</h5>
                        <p class="text-muted">لم يتم العثور على كاميرات تطابق معايير البحث</p>
                        {% if current_user.has_permission('can_manage_cameras') %}
                        <a href="{{ url_for('cameras.add') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة كاميرا جديدة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث حالة الكاميرات كل 30 ثانية
    setInterval(updateCameraStatus, 30000);
    
    // تحديد/إلغاء تحديد جميع الكاميرات
    $('#selectAll').change(function() {
        $('.camera-checkbox').prop('checked', this.checked);
    });
    
    // تحديث حالة "تحديد الكل" عند تغيير الكاميرات الفردية
    $('.camera-checkbox').change(function() {
        const total = $('.camera-checkbox').length;
        const checked = $('.camera-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });
});

function updateCameraStatus() {
    $.ajax({
        url: '/cameras/api/status',
        method: 'GET',
        success: function(cameras) {
            cameras.forEach(function(camera) {
                const row = $(`tr[data-camera-id="${camera.id}"]`);
                const statusCell = row.find('.camera-status');
                
                if (camera.is_connected) {
                    statusCell.removeClass('offline').addClass('online');
                    statusCell.html('<i class="fas fa-circle me-1"></i>متصل');
                } else {
                    statusCell.removeClass('online').addClass('offline');
                    statusCell.html('<i class="fas fa-circle me-1"></i>غير متصل');
                }
            });
        },
        error: function() {
            console.log('خطأ في تحديث حالة الكاميرات');
        }
    });
}

function refreshCameraList() {
    showLoading();
    location.reload();
}

function selectAllCameras() {
    $('.camera-checkbox').prop('checked', true);
    $('#selectAll').prop('checked', true);
}

function bulkActions() {
    const selectedCameras = $('.camera-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedCameras.length === 0) {
        alert('يرجى تحديد كاميرا واحدة على الأقل');
        return;
    }
    
    // يمكن إضافة نافذة منبثقة للعمليات المجمعة هنا
    console.log('الكاميرات المحددة:', selectedCameras);
}

function deleteCamera(cameraId, cameraName) {
    if (confirm(`هل أنت متأكد من حذف الكاميرا "${cameraName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        $.ajax({
            url: `/cameras/${cameraId}/delete`,
            method: 'POST',
            headers: {
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    SurveillanceSystem.showToast(response.message, 'success');
                    $(`tr[data-camera-id="${cameraId}"]`).fadeOut(function() {
                        $(this).remove();
                    });
                } else {
                    SurveillanceSystem.showToast(response.message, 'error');
                }
            },
            error: function() {
                SurveillanceSystem.showToast('خطأ في حذف الكاميرا', 'error');
            }
        });
    }
}
</script>
{% endblock %}
