{% extends "base.html" %}
{% set title = "لوحة التحكم" %}

{% block content %}
<div class="container-fluid">
    
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-gradient mb-1">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </h2>
                    <p class="text-muted mb-0">نظرة عامة على حالة النظام والكاميرات</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        
        <!-- إجمالي الكاميرات -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="totalCameras">{{ stats.cameras.total }}</div>
                        <div class="stats-label">إجمالي الكاميرات</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الكاميرات المتصلة -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="onlineCameras">{{ stats.cameras.online }}</div>
                        <div class="stats-label">كاميرات متصلة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الكاميرات المسجلة -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="recordingCameras">{{ stats.cameras.recording }}</div>
                        <div class="stats-label">كاميرات تسجل</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-record-vinyl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- التنبيهات غير المقروءة -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ stats.alerts.unread }}</div>
                        <div class="stats-label">تنبيهات جديدة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        
        <!-- إحصائيات التسجيلات -->
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        التسجيلات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ stats.recordings.total }}</h4>
                                <small class="text-muted">إجمالي التسجيلات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">{{ stats.recordings.today }}</h4>
                            <small class="text-muted">تسجيلات اليوم</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-hdd me-1"></i>
                            مساحة التخزين: {{ stats.recordings.storage_mb }} ميجابايت
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- حالة النظام -->
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>
                        حالة النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>قاعدة البيانات</span>
                        <span class="badge bg-success">متصلة</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>خدمة البث</span>
                        <span class="badge bg-success">تعمل</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>كشف الحركة</span>
                        <span class="badge bg-warning">جزئي</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>التنبيهات</span>
                        <span class="badge bg-success">نشطة</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الإجراءات السريعة -->
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('cameras.add') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            إضافة كاميرا جديدة
                        </a>
                        <a href="{{ url_for('cameras.live_view') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            المشاهدة المباشرة
                        </a>
                        <a href="{{ url_for('recordings.index') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-video me-1"></i>
                            عرض التسجيلات
                        </a>
                        {% if current_user.is_admin %}
                        <a href="{{ url_for('settings.index') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-cog me-1"></i>
                            إعدادات النظام
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <!-- الجداول -->
    <div class="row">
        
        <!-- الكاميرات الحديثة -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        الكاميرات الحديثة
                    </h5>
                    <a href="{{ url_for('cameras.index') }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if recent_cameras %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الحالة</th>
                                    <th>النوع</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for camera in recent_cameras %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-camera text-muted me-2"></i>
                                            <div>
                                                <div class="fw-bold">{{ camera.name }}</div>
                                                <small class="text-muted">{{ camera.ip_address }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="camera-status {{ 'online' if camera.is_online() else 'offline' }}">
                                            <i class="fas fa-circle me-1"></i>
                                            {{ 'متصل' if camera.is_online() else 'غير متصل' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ camera.camera_type }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ camera.created_at.strftime('%Y-%m-%d') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كاميرات مضافة بعد</p>
                        <a href="{{ url_for('cameras.add') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة كاميرا جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- التنبيهات الحديثة -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        التنبيهات الحديثة
                    </h5>
                    <a href="{{ url_for('dashboard.alerts') }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if recent_alerts %}
                    <div class="list-group list-group-flush">
                        {% for alert in recent_alerts %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="me-auto">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="{{ alert.get_type_icon() }} text-{{ alert.get_severity_color() }} me-2"></i>
                                    <h6 class="mb-0">{{ alert.title }}</h6>
                                </div>
                                <p class="mb-1 small text-muted">{{ alert.message[:100] }}...</p>
                                <small class="text-muted">{{ alert.get_age_formatted() }}</small>
                            </div>
                            <span class="badge bg-{{ alert.get_severity_color() }} rounded-pill">
                                {{ alert.severity }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد تنبيهات حديثة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
    </div>
    
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        updateDashboardStats();
    }, 30000);
    
    function updateDashboardStats() {
        $.ajax({
            url: '/dashboard/api/stats',
            method: 'GET',
            success: function(data) {
                $('#totalCameras').text(data.total_cameras);
                $('#onlineCameras').text(data.online_cameras);
                $('#recordingCameras').text(data.recording_cameras);
                
                // تحديث عداد التنبيهات في شريط التنقل
                SurveillanceSystem.updateAlertsCount(data.unread_alerts);
            },
            error: function() {
                console.log('خطأ في تحديث الإحصائيات');
            }
        });
    }
    
    // تأثيرات بصرية للبطاقات
    $('.stats-card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
});
</script>
{% endblock %}
