# -*- coding: utf-8 -*-
"""
مسارات البث المباشر
Streaming Routes
"""

from flask import Blueprint, Response, render_template, jsonify, request
from flask_login import login_required, current_user
import cv2
import time
import threading
from datetime import datetime

from app.models import Camera
from app.utils.camera_manager import camera_manager

bp = Blueprint('streaming', __name__)

def generate_frames(camera_id):
    """مولد الإطارات للبث المباشر"""
    while True:
        try:
            frame_data = camera_manager.get_frame(camera_id)
            
            if frame_data:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
            else:
                # إرسال إطار فارغ أو رسالة خطأ
                error_frame = create_error_frame("لا يمكن الاتصال بالكاميرا")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            
            time.sleep(0.1)  # تأخير صغير لتقليل استهلاك المعالج
            
        except Exception as e:
            print(f"خطأ في توليد الإطارات: {e}")
            break

def create_error_frame(message):
    """إنشاء إطار خطأ"""
    import numpy as np
    
    # إنشاء صورة سوداء
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # إضافة النص
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img, message, (50, 240), font, 1, (255, 255, 255), 2)
    
    # تحويل إلى JPEG
    ret, buffer = cv2.imencode('.jpg', img)
    return buffer.tobytes()

@bp.route('/camera/<int:camera_id>')
@login_required
def video_feed(camera_id):
    """بث مباشر للكاميرا"""
    
    # التحقق من وجود الكاميرا والصلاحيات
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_view_cameras'):
        return jsonify({'error': 'غير مسموح لك بمشاهدة الكاميرات'}), 403
    
    # التحقق من إمكانية الوصول للكاميرا
    accessible_cameras = current_user.get_accessible_cameras()
    if camera not in accessible_cameras:
        return jsonify({'error': 'غير مسموح لك بمشاهدة هذه الكاميرا'}), 403
    
    return Response(
        generate_frames(camera_id),
        mimetype='multipart/x-mixed-replace; boundary=frame'
    )

@bp.route('/snapshot/<int:camera_id>')
@login_required
def snapshot(camera_id):
    """التقاط صورة من الكاميرا"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_view_cameras'):
        return jsonify({'error': 'غير مسموح'}), 403
    
    frame_data = camera_manager.get_frame(camera_id)
    
    if frame_data:
        return Response(
            frame_data,
            mimetype='image/jpeg',
            headers={
                'Content-Disposition': f'attachment; filename=snapshot_{camera.name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.jpg'
            }
        )
    else:
        # إرجاع صورة خطأ
        error_frame = create_error_frame("لا يمكن التقاط الصورة")
        return Response(
            error_frame,
            mimetype='image/jpeg'
        )

@bp.route('/status/<int:camera_id>')
@login_required
def stream_status(camera_id):
    """حالة البث للكاميرا"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    connection_info = camera_manager.get_connection_info(camera_id)
    
    if connection_info:
        return jsonify({
            'connected': True,
            'type': connection_info['type'],
            'last_update': connection_info['last_update'].isoformat(),
            'camera_name': camera.name
        })
    else:
        return jsonify({
            'connected': False,
            'camera_name': camera.name
        })

@bp.route('/start/<int:camera_id>', methods=['POST'])
@login_required
def start_stream(camera_id):
    """بدء البث للكاميرا"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_control_cameras'):
        return jsonify({'error': 'غير مسموح لك بالتحكم في الكاميرات'}), 403
    
    # محاولة الاتصال بالكاميرا
    success = camera_manager._attempt_connection(camera)
    
    if success:
        return jsonify({
            'success': True,
            'message': f'تم بدء البث للكاميرا {camera.name}'
        })
    else:
        return jsonify({
            'success': False,
            'message': f'فشل في بدء البث للكاميرا {camera.name}'
        })

@bp.route('/stop/<int:camera_id>', methods=['POST'])
@login_required
def stop_stream(camera_id):
    """إيقاف البث للكاميرا"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_control_cameras'):
        return jsonify({'error': 'غير مسموح لك بالتحكم في الكاميرات'}), 403
    
    camera_manager.disconnect_camera(camera_id)
    
    return jsonify({
        'success': True,
        'message': f'تم إيقاف البث للكاميرا {camera.name}'
    })

@bp.route('/multi-view')
@login_required
def multi_view():
    """عرض متعدد الكاميرات"""
    
    if not current_user.has_permission('can_view_cameras'):
        return render_template('errors/403.html'), 403
    
    # الحصول على الكاميرات المتاحة
    cameras = current_user.get_accessible_cameras()
    online_cameras = [cam for cam in cameras if cam.is_online()]
    
    return render_template('streaming/multi_view.html', 
                         cameras=online_cameras)

@bp.route('/live-view/<int:camera_id>')
@login_required
def live_view(camera_id):
    """عرض مباشر لكاميرا واحدة"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_view_cameras'):
        return render_template('errors/403.html'), 403
    
    # التحقق من إمكانية الوصول
    accessible_cameras = current_user.get_accessible_cameras()
    if camera not in accessible_cameras:
        return render_template('errors/403.html'), 403
    
    return render_template('streaming/live_view.html', camera=camera)

@bp.route('/fullscreen/<int:camera_id>')
@login_required
def fullscreen_view(camera_id):
    """عرض ملء الشاشة"""
    
    camera = Camera.query.get_or_404(camera_id)
    
    if not current_user.has_permission('can_view_cameras'):
        return render_template('errors/403.html'), 403
    
    return render_template('streaming/fullscreen.html', camera=camera)

@bp.route('/api/cameras/online')
@login_required
def api_online_cameras():
    """API للكاميرات المتصلة"""
    
    cameras = current_user.get_accessible_cameras()
    online_cameras = []
    
    for camera in cameras:
        if camera_manager.is_camera_connected(camera.id):
            online_cameras.append({
                'id': camera.id,
                'name': camera.name,
                'location': camera.location,
                'type': camera.camera_type,
                'stream_url': f'/stream/camera/{camera.id}'
            })
    
    return jsonify(online_cameras)

@bp.route('/api/stream/health')
@login_required
def stream_health():
    """فحص صحة خدمة البث"""
    
    active_streams = len(camera_manager.active_connections)
    total_cameras = Camera.query.filter_by(is_active=True).count()
    
    return jsonify({
        'active_streams': active_streams,
        'total_cameras': total_cameras,
        'health_status': 'healthy' if active_streams > 0 else 'no_streams',
        'timestamp': datetime.utcnow().isoformat()
    })
