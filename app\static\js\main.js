/**
 * نظام مراقبة الكاميرات - JavaScript الرئيسي
 * Main JavaScript for Surveillance Camera System
 */

// إعدادات عامة
const SurveillanceSystem = {
    config: {
        refreshInterval: 30000, // 30 ثانية
        alertCheckInterval: 10000, // 10 ثواني
        streamTimeout: 15000, // 15 ثانية
        maxRetries: 3
    },
    
    // حالة النظام
    state: {
        isOnline: true,
        activeStreams: new Map(),
        alertsCount: 0,
        lastUpdate: null
    },
    
    // تهيئة النظام
    init: function() {
        console.log('🚀 تهيئة نظام مراقبة الكاميرات...');
        
        this.setupEventListeners();
        this.startPeriodicUpdates();
        this.checkSystemStatus();
        
        if (window.APP_CONFIG.isAuthenticated) {
            this.loadDashboardData();
            this.startAlertMonitoring();
        }
        
        console.log('✅ تم تهيئة النظام بنجاح');
    },
    
    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // تحديث حالة النظام
        $(document).on('visibilitychange', () => {
            if (!document.hidden) {
                this.checkSystemStatus();
            }
        });
        
        // معالجة أخطاء الشبكة
        $(document).ajaxError((event, xhr, settings, error) => {
            if (xhr.status === 0) {
                this.handleNetworkError();
            }
        });
        
        // تأكيد الحذف
        $(document).on('click', '[data-confirm]', function(e) {
            const message = $(this).data('confirm') || 'هل أنت متأكد من هذا الإجراء؟';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تحديث تلقائي للجداول
        $(document).on('click', '[data-refresh]', function() {
            const target = $(this).data('refresh');
            SurveillanceSystem.refreshTable(target);
        });
    },
    
    // بدء التحديثات الدورية
    startPeriodicUpdates: function() {
        setInterval(() => {
            if (this.state.isOnline) {
                this.updateSystemStatus();
            }
        }, this.config.refreshInterval);
    },
    
    // فحص حالة النظام
    checkSystemStatus: function() {
        $.ajax({
            url: '/api/system/status',
            method: 'GET',
            timeout: 5000,
            success: (data) => {
                this.updateSystemIndicators(data);
                this.state.isOnline = true;
            },
            error: () => {
                this.handleNetworkError();
            }
        });
    },
    
    // تحديث مؤشرات النظام
    updateSystemIndicators: function(data) {
        // تحديث حالة النظام في شريط التنقل
        const statusIcon = $('#systemStatus');
        const statusText = $('#systemStatusText');
        
        if (data.status === 'online') {
            statusIcon.removeClass('text-danger text-warning').addClass('text-success');
            statusText.text('متصل');
        } else if (data.status === 'warning') {
            statusIcon.removeClass('text-success text-danger').addClass('text-warning');
            statusText.text('تحذير');
        } else {
            statusIcon.removeClass('text-success text-warning').addClass('text-danger');
            statusText.text('غير متصل');
        }
        
        this.state.lastUpdate = new Date();
    },
    
    // معالجة أخطاء الشبكة
    handleNetworkError: function() {
        this.state.isOnline = false;
        
        const statusIcon = $('#systemStatus');
        const statusText = $('#systemStatusText');
        
        statusIcon.removeClass('text-success text-warning').addClass('text-danger');
        statusText.text('انقطاع الاتصال');
        
        this.showToast('انقطع الاتصال بالخادم', 'error');
    },
    
    // تحميل بيانات لوحة التحكم
    loadDashboardData: function() {
        $.ajax({
            url: '/api/dashboard/stats',
            method: 'GET',
            success: (data) => {
                this.updateDashboardStats(data);
            },
            error: (xhr) => {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', xhr);
            }
        });
    },
    
    // تحديث إحصائيات لوحة التحكم
    updateDashboardStats: function(data) {
        // تحديث عدد الكاميرات
        $('#totalCameras').text(data.total_cameras || 0);
        $('#onlineCameras').text(data.online_cameras || 0);
        $('#recordingCameras').text(data.recording_cameras || 0);
        
        // تحديث التسجيلات
        $('#totalRecordings').text(data.total_recordings || 0);
        $('#todayRecordings').text(data.today_recordings || 0);
        
        // تحديث التنبيهات
        this.updateAlertsCount(data.unread_alerts || 0);
    },
    
    // بدء مراقبة التنبيهات
    startAlertMonitoring: function() {
        setInterval(() => {
            this.checkNewAlerts();
        }, this.config.alertCheckInterval);
    },
    
    // فحص التنبيهات الجديدة
    checkNewAlerts: function() {
        $.ajax({
            url: '/api/alerts/count',
            method: 'GET',
            success: (data) => {
                this.updateAlertsCount(data.count);
            }
        });
    },
    
    // تحديث عدد التنبيهات
    updateAlertsCount: function(count) {
        const alertsBadge = $('#alertsCount');
        
        if (count > 0) {
            alertsBadge.text(count).show();
            
            // إشعار صوتي للتنبيهات الجديدة
            if (count > this.state.alertsCount) {
                this.playNotificationSound();
            }
        } else {
            alertsBadge.hide();
        }
        
        this.state.alertsCount = count;
    },
    
    // تشغيل صوت الإشعار
    playNotificationSound: function() {
        try {
            const audio = new Audio('/static/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(e => {
                console.log('لا يمكن تشغيل الصوت:', e);
            });
        } catch (e) {
            console.log('خطأ في تشغيل الصوت:', e);
        }
    },
    
    // عرض رسالة منبثقة
    showToast: function(message, type = 'info', duration = 5000) {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        // إضافة التوست إلى الحاوية
        let toastContainer = $('#toastContainer');
        if (toastContainer.length === 0) {
            $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
            toastContainer = $('#toastContainer');
        }
        
        const toastElement = $(toastHtml);
        toastContainer.append(toastElement);
        
        // تفعيل التوست
        const toast = new bootstrap.Toast(toastElement[0], {
            autohide: true,
            delay: duration
        });
        toast.show();
        
        // إزالة التوست بعد الإخفاء
        toastElement.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    },
    
    // تحديث جدول
    refreshTable: function(tableId) {
        const table = $(`#${tableId}`);
        if (table.length === 0) return;
        
        const refreshUrl = table.data('refresh-url');
        if (!refreshUrl) return;
        
        showLoading();
        
        $.ajax({
            url: refreshUrl,
            method: 'GET',
            success: (data) => {
                table.html(data);
                hideLoading();
                this.showToast('تم تحديث البيانات بنجاح', 'success');
            },
            error: () => {
                hideLoading();
                this.showToast('خطأ في تحديث البيانات', 'error');
            }
        });
    },
    
    // إدارة البث المباشر
    streaming: {
        // بدء البث
        start: function(cameraId, containerId) {
            const container = $(`#${containerId}`);
            if (container.length === 0) return;
            
            const streamUrl = `/stream/camera/${cameraId}`;
            const img = $(`<img src="${streamUrl}" class="img-fluid" alt="بث مباشر">`);
            
            img.on('load', function() {
                container.html(img);
                SurveillanceSystem.state.activeStreams.set(cameraId, {
                    containerId: containerId,
                    startTime: new Date()
                });
            });
            
            img.on('error', function() {
                container.html(`
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <div>لا يمكن الاتصال بالكاميرا</div>
                        <button class="btn btn-sm btn-primary mt-2" onclick="SurveillanceSystem.streaming.retry(${cameraId}, '${containerId}')">
                            <i class="fas fa-redo me-1"></i>إعادة المحاولة
                        </button>
                    </div>
                `);
            });
        },
        
        // إيقاف البث
        stop: function(cameraId) {
            const streamInfo = SurveillanceSystem.state.activeStreams.get(cameraId);
            if (streamInfo) {
                $(`#${streamInfo.containerId}`).empty();
                SurveillanceSystem.state.activeStreams.delete(cameraId);
            }
        },
        
        // إعادة المحاولة
        retry: function(cameraId, containerId) {
            this.stop(cameraId);
            setTimeout(() => {
                this.start(cameraId, containerId);
            }, 1000);
        }
    },
    
    // أدوات مساعدة
    utils: {
        // تنسيق التاريخ
        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },
        
        // تنسيق حجم الملف
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 بايت';
            
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // تنسيق المدة
        formatDuration: function(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }
};

// تهيئة النظام عند تحميل الصفحة
$(document).ready(function() {
    SurveillanceSystem.init();
});

// تصدير للاستخدام العام
window.SurveillanceSystem = SurveillanceSystem;
