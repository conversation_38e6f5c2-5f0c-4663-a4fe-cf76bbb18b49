#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مبسط لاختبار النظام
Minimal App for Testing
"""

from flask import Flask, render_template_string, redirect, url_for, request, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'surveillance-system-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///surveillance_minimal.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نموذج المستخدم
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); min-height: 100vh; }
        .auth-card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .auth-logo { animation: pulse 2s infinite; }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card auth-card">
                    <div class="card-header text-center bg-primary text-white">
                        <div class="auth-logo mb-3">
                            <i class="fas fa-video fa-4x"></i>
                        </div>
                        <h3>نظام مراقبة الكاميرات</h3>
                        <p class="mb-0">حلول متقدمة للمراقبة الأمنية</p>
                    </div>
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h5 class="text-primary">مرحباً بك في النظام</h5>
                            <p class="text-muted">نظام شامل لإدارة ومراقبة الكاميرات الأمنية</p>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-6 text-center">
                                <i class="fas fa-camera fa-2x text-primary mb-2"></i>
                                <div><small>دعم جميع الكاميرات</small></div>
                            </div>
                            <div class="col-6 text-center">
                                <i class="fas fa-eye fa-2x text-success mb-2"></i>
                                <div><small>مشاهدة مباشرة</small></div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    ''')

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); min-height: 100vh; }
        .auth-card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-4">
                <div class="card auth-card">
                    <div class="card-header text-center bg-primary text-white">
                        <i class="fas fa-video fa-3x mb-2"></i>
                        <h4>تسجيل الدخول</h4>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                المستخدم الافتراضي: admin / admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .stats-card { background: linear-gradient(135deg, #2c3e50, #3498db); color: white; border-radius: 15px; }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-video me-2"></i>نظام المراقبة
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ current_user.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- المحتوى الرئيسي -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                <p class="text-muted">مرحباً {{ current_user.username }}، هذه نظرة عامة على النظام</p>
            </div>
        </div>
        
        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>0</h3>
                            <p class="mb-0">الكاميرات</p>
                        </div>
                        <i class="fas fa-camera fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>0</h3>
                            <p class="mb-0">متصلة</p>
                        </div>
                        <i class="fas fa-wifi fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>0</h3>
                            <p class="mb-0">تسجيلات</p>
                        </div>
                        <i class="fas fa-video fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-danger text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>0</h3>
                            <p class="mb-0">تنبيهات</p>
                        </div>
                        <i class="fas fa-bell fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الإجراءات السريعة -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" disabled>
                                <i class="fas fa-plus me-2"></i>إضافة كاميرا جديدة
                            </button>
                            <button class="btn btn-success" disabled>
                                <i class="fas fa-eye me-2"></i>المشاهدة المباشرة
                            </button>
                            <button class="btn btn-info" disabled>
                                <i class="fas fa-video me-2"></i>عرض التسجيلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>حالة النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>قاعدة البيانات</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>خدمة البث</span>
                            <span class="badge bg-success">تعمل</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>النظام</span>
                            <span class="badge bg-success">جاهز</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- رسالة النجاح -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>تم تشغيل النظام بنجاح!</h5>
                    <p class="mb-0">
                        نظام مراقبة الكاميرات يعمل بشكل صحيح. يمكنك الآن البدء في إضافة الكاميرات وإعداد النظام.
                        هذا إصدار تجريبي يحتوي على الوظائف الأساسية.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

# تشغيل التطبيق
if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء المستخدم الافتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin/admin123")
    
    print("\n" + "="*60)
    print("🎉 نظام مراقبة الكاميرات - الإصدار التجريبي")
    print("="*60)
    print("🌐 الواجهة متاحة على: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("="*60)
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
