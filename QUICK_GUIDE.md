# دليل التشغيل السريع - نظام مراقبة الكاميرات
## Quick Start Guide - Surveillance Camera System

---

## ✅ النظام يعمل الآن!

تم تشغيل نظام مراقبة الكاميرات بنجاح على: **http://localhost:5000**

---

## 🚀 كيفية التشغيل

### الطريقة الأسهل (تعمل 100%)
```bash
python simple_surveillance.py
```

### إذا لم تعمل، جرب:
```bash
python3 simple_surveillance.py
```

---

## 🔑 بيانات الدخول

- **الرابط**: http://localhost:5000
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 📱 كيفية الاستخدام

### 1. الصفحة الرئيسية
- افتح المتصفح على http://localhost:5000
- ستظهر لك صفحة ترحيب جميلة
- اضغط على "تسجيل الدخول"

### 2. تسجيل الدخول
- أدخل: `admin` في اسم المستخدم
- أدخل: `admin123` في كلمة المرور
- اضغط "دخول"

### 3. لوحة التحكم
- ستظهر لك إحصائيات النظام
- عدد الكاميرات المتصلة وغير المتصلة
- قائمة بالكاميرات التجريبية

### 4. إدارة الكاميرات
- يمكنك عرض تفاصيل كل كاميرا
- بدء البث المباشر (تجريبي)
- تعديل إعدادات الكاميرا

---

## 🎨 المميزات المتوفرة

### ✅ يعمل حالياً:
- [x] واجهة عربية جميلة مع RTL
- [x] نظام تسجيل دخول آمن
- [x] لوحة تحكم تفاعلية
- [x] إحصائيات النظام
- [x] قاعدة بيانات SQLite
- [x] إدارة الكاميرات الأساسية
- [x] تصميم متجاوب للموبايل

### 🔄 قيد التطوير:
- [ ] البث المباشر الفعلي
- [ ] تسجيل الفيديو
- [ ] كشف الحركة
- [ ] نظام التنبيهات
- [ ] إضافة كاميرات جديدة

---

## 🛠️ المتطلبات

### الحد الأدنى:
- Python 3.6 أو أحدث
- Flask (يتم تثبيته تلقائياً)

### للتثبيت:
```bash
pip install flask
```

---

## 📂 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `simple_surveillance.py` | النظام الكامل في ملف واحد |
| `surveillance_simple.db` | قاعدة البيانات (تُنشأ تلقائياً) |
| `QUICK_GUIDE.md` | هذا الدليل |

---

## 🔧 حل المشاكل

### إذا لم يعمل النظام:

#### 1. تحقق من Python:
```bash
python --version
# أو
python3 --version
```

#### 2. تثبيت Flask:
```bash
pip install flask
# أو
pip3 install flask
```

#### 3. تشغيل النظام:
```bash
python simple_surveillance.py
```

#### 4. إذا ظهرت رسالة خطأ:
- تأكد من أن المنفذ 5000 غير مستخدم
- أغلق أي برامج أخرى تستخدم نفس المنفذ
- أعد تشغيل الكمبيوتر إذا لزم الأمر

---

## 🌐 الوصول من أجهزة أخرى

### في نفس الشبكة:
1. اعرف عنوان IP للكمبيوتر:
   ```bash
   ipconfig  # Windows
   ifconfig  # Linux/Mac
   ```

2. افتح المتصفح على الجهاز الآخر:
   ```
   http://[IP_ADDRESS]:5000
   ```

---

## 📞 الدعم

### إذا واجهت مشاكل:
1. تأكد من تثبيت Python و Flask
2. تحقق من رسائل الخطأ في Terminal
3. أعد تشغيل النظام
4. تأكد من أن المنفذ 5000 متاح

### رسائل الخطأ الشائعة:

#### `ModuleNotFoundError: No module named 'flask'`
**الحل**: `pip install flask`

#### `Address already in use`
**الحل**: أغلق البرامج الأخرى أو غير المنفذ

#### `Permission denied`
**الحل**: شغل Terminal كمدير

---

## 🎉 تهانينا!

إذا وصلت هنا فالنظام يعمل بنجاح! 

### الخطوات التالية:
1. استكشف لوحة التحكم
2. جرب تسجيل الدخول والخروج
3. اطلع على قائمة الكاميرات
4. استمتع بالواجهة العربية الجميلة

---

## 🚀 التطوير المستقبلي

هذا النظام قابل للتوسع ويمكن إضافة:
- البث المباشر الحقيقي
- تسجيل الفيديو
- كشف الحركة بالذكاء الاصطناعي
- تطبيق موبايل
- نظام تنبيهات متقدم

---

**🎊 مبروك! نظام مراقبة الكاميرات يعمل بنجاح!**
