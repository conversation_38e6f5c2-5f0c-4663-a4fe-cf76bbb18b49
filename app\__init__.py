# -*- coding: utf-8 -*-
"""
تهيئة التطبيق الرئيسي
Main Application Initialization
"""

import os
from flask import Flask, request, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config.config import config

# تهيئة الإضافات
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_name=None):
    """إنشاء وتكوين التطبيق"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة الإضافات
    db.init_app(app)
    login_manager.init_app(app)
    
    # إعدادات تسجيل الدخول
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))
    
    # إعدادات اللغة (مبسطة)
    def get_locale():
        return session.get('language', 'ar')
    
    # تسجيل المسارات (Blueprints)
    from app.routes.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.routes.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.routes.dashboard import bp as dashboard_bp
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    
    from app.routes.cameras import bp as cameras_bp
    app.register_blueprint(cameras_bp, url_prefix='/cameras')
    
    from app.routes.streaming import bp as streaming_bp
    app.register_blueprint(streaming_bp, url_prefix='/stream')
    
    from app.routes.recordings import bp as recordings_bp
    app.register_blueprint(recordings_bp, url_prefix='/recordings')
    
    from app.routes.settings import bp as settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')
    
    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # إضافة متغيرات السياق العامة
    @app.context_processor
    def inject_conf_vars():
        return {
            'LANGUAGES': ['ar', 'en'],
            'CURRENT_LANGUAGE': get_locale()
        }
    
    return app

# استيراد النماذج لضمان إنشاء الجداول
from app import models
