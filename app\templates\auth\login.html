{% extends "base.html" %}
{% set title = "تسجيل الدخول" %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                
                <!-- بطاقة تسجيل الدخول -->
                <div class="card auth-card shadow-lg">
                    
                    <!-- رأس البطاقة -->
                    <div class="card-header text-center bg-primary text-white">
                        <div class="auth-logo mb-3">
                            <i class="fas fa-video fa-3x"></i>
                        </div>
                        <h4 class="mb-0">نظام مراقبة الكاميرات</h4>
                        <p class="mb-0 opacity-75">تسجيل الدخول إلى النظام</p>
                    </div>
                    
                    <!-- جسم البطاقة -->
                    <div class="card-body p-4">
                        
                        <form method="POST" action="{{ url_for('auth.login') }}" novalidate>
                            {{ form.hidden_tag() }}
                            
                            <!-- اسم المستخدم -->
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>{{ form.username.label.text }}
                                </label>
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- كلمة المرور -->
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>{{ form.password.label.text }}
                                </label>
                                <div class="input-group">
                                    {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), id="passwordField") }}
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.password.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- تذكرني -->
                            <div class="mb-3 form-check">
                                {{ form.remember_me(class="form-check-input") }}
                                <label class="form-check-label" for="remember_me">
                                    {{ form.remember_me.label.text }}
                                </label>
                            </div>
                            
                            <!-- زر تسجيل الدخول -->
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>
                            
                        </form>
                        
                        <!-- روابط إضافية -->
                        <div class="text-center mt-4">
                            <div class="mb-2">
                                <a href="#" class="text-decoration-none small">
                                    <i class="fas fa-question-circle me-1"></i>
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                            
                            <!-- رابط التسجيل (يمكن إخفاؤه في الإنتاج) -->
                            {% if config.ALLOW_REGISTRATION %}
                            <div>
                                <span class="text-muted small">ليس لديك حساب؟</span>
                                <a href="{{ url_for('auth.register') }}" class="text-decoration-none small">
                                    إنشاء حساب جديد
                                </a>
                            </div>
                            {% endif %}
                        </div>
                        
                    </div>
                    
                    <!-- تذييل البطاقة -->
                    <div class="card-footer text-center text-muted small">
                        <i class="fas fa-shield-alt me-1"></i>
                        نظام آمن ومحمي
                    </div>
                    
                </div>
                
                <!-- معلومات النظام -->
                <div class="text-center mt-4">
                    <div class="card bg-light">
                        <div class="card-body py-3">
                            <h6 class="card-title mb-2">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات النظام
                            </h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-primary">
                                        <i class="fas fa-camera fa-2x"></i>
                                        <div class="small mt-1">دعم جميع الكاميرات</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-success">
                                        <i class="fas fa-record-vinyl fa-2x"></i>
                                        <div class="small mt-1">تسجيل عالي الجودة</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-warning">
                                        <i class="fas fa-bell fa-2x"></i>
                                        <div class="small mt-1">تنبيهات ذكية</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تبديل إظهار/إخفاء كلمة المرور
    $('#togglePassword').click(function() {
        const passwordField = $('#passwordField');
        const toggleIcon = $('#togglePasswordIcon');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // التركيز على حقل اسم المستخدم
    $('#username').focus();
    
    // تأثيرات بصرية
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
});
</script>
{% endblock %}
