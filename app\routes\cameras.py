# -*- coding: utf-8 -*-
"""
مسارات إدارة الكاميرات
Camera Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime

from app import db
from app.models import Camera, Alert
from app.forms.camera import CameraForm, CameraEditForm
from app.utils.camera_manager import camera_manager

bp = Blueprint('cameras', __name__)

@bp.route('/')
@login_required
def index():
    """قائمة الكاميرات"""
    
    if not current_user.has_permission('can_view_cameras'):
        flash('غير مسموح لك بعرض الكاميرات', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # فلترة الكاميرات
    camera_type = request.args.get('type')
    status = request.args.get('status')
    search = request.args.get('search')
    
    query = Camera.query
    
    # تطبيق الفلاتر
    if camera_type:
        query = query.filter_by(camera_type=camera_type)
    
    if status:
        if status == 'online':
            query = query.filter_by(connection_status='connected')
        elif status == 'offline':
            query = query.filter(Camera.connection_status != 'connected')
        elif status == 'recording':
            query = query.filter_by(is_recording=True)
    
    if search:
        query = query.filter(
            Camera.name.contains(search) | 
            Camera.location.contains(search) |
            Camera.ip_address.contains(search)
        )
    
    # ترتيب وتقسيم الصفحات
    cameras_pagination = query.order_by(Camera.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات الكاميرات
    stats = {
        'total': Camera.query.count(),
        'online': Camera.query.filter_by(connection_status='connected').count(),
        'offline': Camera.query.filter(Camera.connection_status != 'connected').count(),
        'recording': Camera.query.filter_by(is_recording=True).count(),
        'by_type': dict(
            db.session.query(Camera.camera_type, db.func.count(Camera.id))
            .group_by(Camera.camera_type).all()
        )
    }
    
    return render_template('cameras/index.html',
                         cameras=cameras_pagination.items,
                         pagination=cameras_pagination,
                         stats=stats,
                         current_filters={
                             'type': camera_type,
                             'status': status,
                             'search': search
                         })

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة كاميرا جديدة"""
    
    if not current_user.has_permission('can_manage_cameras'):
        flash('غير مسموح لك بإضافة كاميرات', 'error')
        return redirect(url_for('cameras.index'))
    
    form = CameraForm()
    
    if form.validate_on_submit():
        camera = Camera(
            name=form.name.data,
            description=form.description.data,
            ip_address=form.ip_address.data,
            port=form.port.data,
            username=form.username.data,
            password=form.password.data,
            camera_type=form.camera_type.data,
            protocol=form.protocol.data,
            brand=form.brand.data,
            model=form.model.data,
            location=form.location.data,
            channel_number=form.channel_number.data,
            total_channels=form.total_channels.data,
            is_active=form.is_active.data,
            recording_enabled=form.recording_enabled.data,
            motion_detection=form.motion_detection.data
        )
        
        # إنشاء رابط البث حسب البروتوكول
        if form.protocol.data == 'RTSP':
            camera.rtsp_url = form.stream_url.data
        elif form.protocol.data == 'HTTP':
            camera.http_url = form.stream_url.data
        elif form.protocol.data == 'ONVIF':
            camera.onvif_url = form.stream_url.data
        
        db.session.add(camera)
        db.session.commit()
        
        flash(f'تم إضافة الكاميرا {camera.name} بنجاح', 'success')
        
        # محاولة الاتصال بالكاميرا
        if camera.is_active:
            success = camera_manager._attempt_connection(camera)
            if success:
                flash('تم الاتصال بالكاميرا بنجاح', 'success')
            else:
                flash('تم إضافة الكاميرا ولكن فشل الاتصال بها', 'warning')
        
        return redirect(url_for('cameras.view', id=camera.id))
    
    return render_template('cameras/add.html', form=form)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الكاميرا"""
    
    camera = Camera.query.get_or_404(id)
    
    if not current_user.has_permission('can_view_cameras'):
        flash('غير مسموح لك بعرض تفاصيل الكاميرا', 'error')
        return redirect(url_for('dashboard.index'))
    
    # التحقق من إمكانية الوصول للكاميرا
    accessible_cameras = current_user.get_accessible_cameras()
    if camera not in accessible_cameras:
        flash('غير مسموح لك بعرض هذه الكاميرا', 'error')
        return redirect(url_for('cameras.index'))
    
    # الحصول على معلومات الاتصال
    connection_info = camera_manager.get_connection_info(id)
    
    # آخر التسجيلات
    recent_recordings = camera.recordings.order_by(
        camera.recordings.property.mapper.class_.created_at.desc()
    ).limit(5).all()
    
    # آخر التنبيهات
    recent_alerts = camera.alerts.order_by(
        camera.alerts.property.mapper.class_.triggered_at.desc()
    ).limit(5).all()
    
    return render_template('cameras/view.html',
                         camera=camera,
                         connection_info=connection_info,
                         recent_recordings=recent_recordings,
                         recent_alerts=recent_alerts)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الكاميرا"""
    
    camera = Camera.query.get_or_404(id)
    
    if not current_user.has_permission('can_manage_cameras'):
        flash('غير مسموح لك بتعديل الكاميرات', 'error')
        return redirect(url_for('cameras.view', id=id))
    
    form = CameraEditForm(obj=camera)
    
    if form.validate_on_submit():
        # حفظ الحالة السابقة
        was_active = camera.is_active
        
        # تحديث البيانات
        form.populate_obj(camera)
        camera.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash(f'تم تحديث الكاميرا {camera.name} بنجاح', 'success')
        
        # إعادة الاتصال إذا تم تفعيل الكاميرا
        if camera.is_active and not was_active:
            camera_manager._attempt_connection(camera)
        elif not camera.is_active and was_active:
            camera_manager.disconnect_camera(camera.id)
        
        return redirect(url_for('cameras.view', id=id))
    
    return render_template('cameras/edit.html', form=form, camera=camera)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف الكاميرا"""
    
    camera = Camera.query.get_or_404(id)
    
    if not current_user.has_permission('can_manage_cameras'):
        return jsonify({'error': 'غير مسموح لك بحذف الكاميرات'}), 403
    
    # قطع الاتصال أولاً
    camera_manager.disconnect_camera(id)
    
    # حذف الكاميرا
    camera_name = camera.name
    db.session.delete(camera)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'تم حذف الكاميرا {camera_name} بنجاح'
    })

@bp.route('/<int:id>/test-connection', methods=['POST'])
@login_required
def test_connection(id):
    """اختبار الاتصال بالكاميرا"""
    
    camera = Camera.query.get_or_404(id)
    
    if not current_user.has_permission('can_control_cameras'):
        return jsonify({'error': 'غير مسموح لك بالتحكم في الكاميرات'}), 403
    
    # محاولة الاتصال
    success = camera_manager._attempt_connection(camera)
    
    if success:
        return jsonify({
            'success': True,
            'message': 'تم الاتصال بالكاميرا بنجاح',
            'status': 'connected'
        })
    else:
        return jsonify({
            'success': False,
            'message': 'فشل في الاتصال بالكاميرا',
            'status': 'disconnected'
        })

@bp.route('/<int:id>/toggle-recording', methods=['POST'])
@login_required
def toggle_recording(id):
    """تشغيل/إيقاف التسجيل"""
    
    camera = Camera.query.get_or_404(id)
    
    if not current_user.has_permission('can_control_cameras'):
        return jsonify({'error': 'غير مسموح لك بالتحكم في التسجيل'}), 403
    
    camera.is_recording = not camera.is_recording
    db.session.commit()
    
    action = 'بدء' if camera.is_recording else 'إيقاف'
    
    return jsonify({
        'success': True,
        'message': f'تم {action} التسجيل للكاميرا {camera.name}',
        'is_recording': camera.is_recording
    })

@bp.route('/live-view')
@login_required
def live_view():
    """المشاهدة المباشرة لجميع الكاميرات"""
    
    if not current_user.has_permission('can_view_cameras'):
        flash('غير مسموح لك بالمشاهدة المباشرة', 'error')
        return redirect(url_for('dashboard.index'))
    
    # الحصول على الكاميرات المتاحة والمتصلة
    accessible_cameras = current_user.get_accessible_cameras()
    online_cameras = [cam for cam in accessible_cameras if cam.is_online()]
    
    return render_template('cameras/live_view.html', cameras=online_cameras)

@bp.route('/multi-view')
@login_required
def multi_view():
    """عرض متعدد الكاميرات"""
    
    if not current_user.has_permission('can_view_cameras'):
        flash('غير مسموح لك بالعرض المتعدد', 'error')
        return redirect(url_for('dashboard.index'))
    
    # الحصول على الكاميرات المتصلة
    accessible_cameras = current_user.get_accessible_cameras()
    online_cameras = [cam for cam in accessible_cameras if cam.is_online()]
    
    # تحديد تخطيط العرض حسب عدد الكاميرات
    camera_count = len(online_cameras)
    if camera_count <= 4:
        layout = '2x2'
    elif camera_count <= 9:
        layout = '3x3'
    elif camera_count <= 16:
        layout = '4x4'
    else:
        layout = '4x4'
        online_cameras = online_cameras[:16]  # حد أقصى 16 كاميرا
    
    return render_template('cameras/multi_view.html',
                         cameras=online_cameras,
                         layout=layout)

@bp.route('/api/status')
@login_required
def api_status():
    """API لحالة جميع الكاميرات"""
    
    cameras = current_user.get_accessible_cameras()
    camera_status = []
    
    for camera in cameras:
        connection_info = camera_manager.get_connection_info(camera.id)
        
        camera_status.append({
            'id': camera.id,
            'name': camera.name,
            'ip_address': camera.ip_address,
            'type': camera.camera_type,
            'protocol': camera.protocol,
            'is_active': camera.is_active,
            'is_recording': camera.is_recording,
            'connection_status': camera.connection_status,
            'is_connected': connection_info is not None,
            'last_seen': camera.last_seen.isoformat() if camera.last_seen else None
        })
    
    return jsonify(camera_status)

@bp.route('/discover', methods=['POST'])
@login_required
def discover():
    """اكتشاف الكاميرات في الشبكة"""
    
    if not current_user.has_permission('can_manage_cameras'):
        return jsonify({'error': 'غير مسموح لك بإدارة الكاميرات'}), 403
    
    # هذه وظيفة متقدمة يمكن تطويرها لاحقاً
    # لاكتشاف الكاميرات تلقائياً في الشبكة
    
    return jsonify({
        'success': True,
        'message': 'ميزة الاكتشاف التلقائي قيد التطوير',
        'discovered_cameras': []
    })
