# -*- coding: utf-8 -*-
"""
مدير التسجيلات
Recording Manager
"""

import cv2
import threading
import time
import os
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any

from app import db
from app.models import Recording, Camera, Alert
from app.utils.camera_manager import camera_manager
from config.config import Config

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RecordingManager:
    """مدير التسجيلات والأرشفة"""
    
    def __init__(self):
        self.active_recordings = {}
        self.recording_threads = {}
        self.is_running = False
        self.motion_detectors = {}
        
    def start_service(self):
        """بدء خدمة التسجيل"""
        self.is_running = True
        
        # بدء مراقبة التسجيل المجدول
        schedule_thread = threading.Thread(target=self._monitor_scheduled_recordings)
        schedule_thread.daemon = True
        schedule_thread.start()
        
        # بدء مراقبة كشف الحركة
        motion_thread = threading.Thread(target=self._monitor_motion_detection)
        motion_thread.daemon = True
        motion_thread.start()
        
        logger.info("تم بدء خدمة التسجيل")
    
    def stop_service(self):
        """إيقاف خدمة التسجيل"""
        self.is_running = False
        
        # إيقاف جميع التسجيلات النشطة
        for camera_id in list(self.active_recordings.keys()):
            self.stop_recording(camera_id)
        
        logger.info("تم إيقاف خدمة التسجيل")
    
    def start_recording(self, camera_id: int, recording_type: str = 'manual', 
                       duration: Optional[int] = None) -> bool:
        """بدء التسجيل لكاميرا"""
        try:
            camera = Camera.query.get(camera_id)
            if not camera:
                logger.error(f"الكاميرا {camera_id} غير موجودة")
                return False
            
            if not camera.is_active:
                logger.warning(f"الكاميرا {camera.name} غير مفعلة")
                return False
            
            # التحقق من وجود تسجيل نشط
            if camera_id in self.active_recordings:
                logger.warning(f"يوجد تسجيل نشط للكاميرا {camera.name}")
                return False
            
            # التحقق من الاتصال بالكاميرا
            if not camera_manager.is_camera_connected(camera_id):
                logger.warning(f"الكاميرا {camera.name} غير متصلة")
                return False
            
            # إنشاء سجل التسجيل
            recording = Recording(
                camera_id=camera_id,
                recording_type=recording_type,
                start_time=datetime.utcnow(),
                status='recording',
                quality=camera.recording_quality or 'medium'
            )
            
            # إنشاء اسم الملف
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"{camera.name}_{timestamp}.mp4"
            recording.filename = filename
            recording.original_filename = filename
            
            # إنشاء مسار الملف
            recordings_dir = os.path.join(Config.RECORDINGS_FOLDER, camera.name)
            os.makedirs(recordings_dir, exist_ok=True)
            
            file_path = os.path.join(recordings_dir, filename)
            recording.file_path = file_path
            
            # حفظ السجل في قاعدة البيانات
            db.session.add(recording)
            db.session.commit()
            
            # بدء التسجيل في خيط منفصل
            recording_thread = threading.Thread(
                target=self._record_video,
                args=(camera_id, recording.id, file_path, duration)
            )
            recording_thread.daemon = True
            recording_thread.start()
            
            # حفظ معلومات التسجيل النشط
            self.active_recordings[camera_id] = {
                'recording_id': recording.id,
                'thread': recording_thread,
                'start_time': datetime.utcnow(),
                'duration': duration
            }
            
            # تحديث حالة الكاميرا
            camera.is_recording = True
            db.session.commit()
            
            logger.info(f"تم بدء التسجيل للكاميرا {camera.name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء التسجيل للكاميرا {camera_id}: {e}")
            return False
    
    def stop_recording(self, camera_id: int) -> bool:
        """إيقاف التسجيل لكاميرا"""
        try:
            if camera_id not in self.active_recordings:
                logger.warning(f"لا يوجد تسجيل نشط للكاميرا {camera_id}")
                return False
            
            recording_info = self.active_recordings[camera_id]
            recording_id = recording_info['recording_id']
            
            # إيقاف التسجيل
            recording = Recording.query.get(recording_id)
            if recording:
                recording.status = 'completed'
                recording.end_time = datetime.utcnow()
                
                # حساب المدة
                if recording.start_time:
                    duration = (recording.end_time - recording.start_time).total_seconds()
                    recording.duration = int(duration)
                
                db.session.commit()
                
                # تحديث معلومات الملف
                recording.update_file_info()
                
                # إنشاء صورة مصغرة
                recording.generate_thumbnail()
            
            # إزالة من التسجيلات النشطة
            del self.active_recordings[camera_id]
            
            # تحديث حالة الكاميرا
            camera = Camera.query.get(camera_id)
            if camera:
                camera.is_recording = False
                db.session.commit()
            
            logger.info(f"تم إيقاف التسجيل للكاميرا {camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف التسجيل للكاميرا {camera_id}: {e}")
            return False
    
    def _record_video(self, camera_id: int, recording_id: int, 
                     file_path: str, duration: Optional[int]):
        """تسجيل الفيديو (يعمل في خيط منفصل)"""
        try:
            # إعداد كاتب الفيديو
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 25
            frame_size = (640, 480)  # يمكن تخصيصه حسب الكاميرا
            
            out = cv2.VideoWriter(file_path, fourcc, fps, frame_size)
            
            start_time = time.time()
            frame_count = 0
            
            while camera_id in self.active_recordings:
                # الحصول على إطار من الكاميرا
                frame_data = camera_manager.get_frame(camera_id)
                
                if frame_data:
                    # تحويل البيانات إلى إطار OpenCV
                    import numpy as np
                    nparr = np.frombuffer(frame_data, np.uint8)
                    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if frame is not None:
                        # تغيير حجم الإطار إذا لزم الأمر
                        frame = cv2.resize(frame, frame_size)
                        
                        # كتابة الإطار
                        out.write(frame)
                        frame_count += 1
                
                # التحقق من انتهاء المدة المحددة
                if duration and (time.time() - start_time) >= duration:
                    break
                
                # تأخير صغير
                time.sleep(1/fps)
            
            # إغلاق كاتب الفيديو
            out.release()
            
            # تحديث معلومات التسجيل
            recording = Recording.query.get(recording_id)
            if recording:
                recording.fps = fps
                recording.resolution = f"{frame_size[0]}x{frame_size[1]}"
                db.session.commit()
            
            logger.info(f"انتهى التسجيل للكاميرا {camera_id}, عدد الإطارات: {frame_count}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الفيديو للكاميرا {camera_id}: {e}")
            
            # تحديث حالة التسجيل إلى فاشل
            recording = Recording.query.get(recording_id)
            if recording:
                recording.status = 'failed'
                db.session.commit()
    
    def _monitor_scheduled_recordings(self):
        """مراقبة التسجيلات المجدولة"""
        while self.is_running:
            try:
                current_time = datetime.utcnow()
                current_hour = current_time.hour
                current_minute = current_time.minute
                current_weekday = current_time.strftime('%A').lower()
                
                # البحث عن الكاميرات التي لديها جدولة تسجيل
                cameras = Camera.query.filter_by(
                    is_active=True,
                    recording_enabled=True
                ).all()
                
                for camera in cameras:
                    schedule = camera.get_recording_schedule()
                    
                    if schedule and current_weekday in schedule:
                        day_schedule = schedule[current_weekday]
                        
                        if 'start' in day_schedule and 'end' in day_schedule:
                            start_time = day_schedule['start']
                            end_time = day_schedule['end']
                            
                            # تحويل الوقت إلى ساعات ودقائق
                            start_hour, start_minute = map(int, start_time.split(':'))
                            end_hour, end_minute = map(int, end_time.split(':'))
                            
                            # التحقق من وقت البداية
                            if (current_hour == start_hour and 
                                current_minute == start_minute and
                                camera.id not in self.active_recordings):
                                
                                # حساب مدة التسجيل
                                duration = (end_hour - start_hour) * 3600 + (end_minute - start_minute) * 60
                                
                                self.start_recording(camera.id, 'scheduled', duration)
                
                # انتظار دقيقة واحدة قبل الفحص التالي
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"خطأ في مراقبة التسجيلات المجدولة: {e}")
                time.sleep(60)
    
    def _monitor_motion_detection(self):
        """مراقبة كشف الحركة"""
        while self.is_running:
            try:
                # البحث عن الكاميرات التي لديها كشف حركة مفعل
                cameras = Camera.query.filter_by(
                    is_active=True,
                    motion_detection=True
                ).all()
                
                for camera in cameras:
                    if camera_manager.is_camera_connected(camera.id):
                        self._check_motion(camera)
                
                time.sleep(1)  # فحص كل ثانية
                
            except Exception as e:
                logger.error(f"خطأ في مراقبة كشف الحركة: {e}")
                time.sleep(5)
    
    def _check_motion(self, camera: Camera):
        """فحص الحركة لكاميرا معينة"""
        try:
            camera_id = camera.id
            
            # الحصول على الإطار الحالي
            frame_data = camera_manager.get_frame(camera_id)
            if not frame_data:
                return
            
            # تحويل إلى إطار OpenCV
            import numpy as np
            nparr = np.frombuffer(frame_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is None:
                return
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray = cv2.GaussianBlur(gray, (21, 21), 0)
            
            # إنشاء كاشف حركة جديد إذا لم يكن موجوداً
            if camera_id not in self.motion_detectors:
                self.motion_detectors[camera_id] = {
                    'background': gray,
                    'last_motion': None
                }
                return
            
            detector = self.motion_detectors[camera_id]
            
            # حساب الفرق
            frame_delta = cv2.absdiff(detector['background'], gray)
            thresh = cv2.threshold(frame_delta, 25, 255, cv2.THRESH_BINARY)[1]
            thresh = cv2.dilate(thresh, None, iterations=2)
            
            # البحث عن الكنتورات
            contours, _ = cv2.findContours(thresh.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            motion_detected = False
            for contour in contours:
                if cv2.contourArea(contour) < camera.motion_threshold:
                    continue
                motion_detected = True
                break
            
            # إذا تم اكتشاف حركة
            if motion_detected:
                current_time = datetime.utcnow()
                
                # تجنب التنبيهات المتكررة (كل 30 ثانية على الأقل)
                if (detector['last_motion'] is None or 
                    (current_time - detector['last_motion']).seconds >= 30):
                    
                    detector['last_motion'] = current_time
                    
                    # حفظ صورة الحركة
                    motion_image_path = self._save_motion_image(camera, frame)
                    
                    # إنشاء تنبيه
                    Alert.create_motion_alert(camera, motion_image_path)
                    
                    # بدء التسجيل إذا لم يكن نشطاً
                    if (camera_id not in self.active_recordings and 
                        camera.recording_enabled):
                        self.start_recording(camera_id, 'motion', 60)  # تسجيل لمدة دقيقة
            
            # تحديث الخلفية
            detector['background'] = gray
            
        except Exception as e:
            logger.error(f"خطأ في فحص الحركة للكاميرا {camera.name}: {e}")
    
    def _save_motion_image(self, camera: Camera, frame) -> Optional[str]:
        """حفظ صورة كشف الحركة"""
        try:
            # إنشاء مجلد الصور
            motion_dir = os.path.join(Config.SNAPSHOTS_FOLDER, 'motion', camera.name)
            os.makedirs(motion_dir, exist_ok=True)
            
            # اسم الملف
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"motion_{timestamp}.jpg"
            file_path = os.path.join(motion_dir, filename)
            
            # حفظ الصورة
            cv2.imwrite(file_path, frame)
            
            return file_path
            
        except Exception as e:
            logger.error(f"خطأ في حفظ صورة الحركة: {e}")
            return None
    
    def get_recording_status(self, camera_id: int) -> Dict[str, Any]:
        """الحصول على حالة التسجيل لكاميرا"""
        if camera_id in self.active_recordings:
            recording_info = self.active_recordings[camera_id]
            elapsed_time = (datetime.utcnow() - recording_info['start_time']).total_seconds()
            
            return {
                'is_recording': True,
                'recording_id': recording_info['recording_id'],
                'elapsed_time': int(elapsed_time),
                'duration': recording_info['duration']
            }
        else:
            return {
                'is_recording': False
            }
    
    def cleanup_old_recordings(self, days_old: int = 30, delete_files: bool = True) -> Dict[str, int]:
        """تنظيف التسجيلات القديمة"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            old_recordings = Recording.query.filter(
                Recording.start_time < cutoff_date,
                Recording.is_protected == False
            ).all()
            
            deleted_count = 0
            freed_space = 0
            
            for recording in old_recordings:
                if delete_files and recording.file_exists():
                    file_size = recording.file_size or 0
                    if recording.delete_file():
                        freed_space += file_size
                
                db.session.delete(recording)
                deleted_count += 1
            
            db.session.commit()
            
            logger.info(f"تم تنظيف {deleted_count} تسجيل قديم")
            
            return {
                'deleted_count': deleted_count,
                'freed_space': freed_space
            }
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التسجيلات القديمة: {e}")
            return {'deleted_count': 0, 'freed_space': 0}

# إنشاء مثيل عام من مدير التسجيلات
recording_manager = RecordingManager()
