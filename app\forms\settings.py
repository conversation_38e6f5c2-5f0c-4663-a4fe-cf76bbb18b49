# -*- coding: utf-8 -*-
"""
نماذج الإعدادات
Settings Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, SelectField, BooleanField, TextAreaField, PasswordField, SubmitField, FloatField
from wtforms.validators import DataRequired, Email, Length, Optional, NumberRange, EqualTo, ValidationError
from app.models import User

class SystemSettingsForm(FlaskForm):
    """نموذج إعدادات النظام العامة"""
    
    # إعدادات عامة
    system_name = StringField('اسم النظام', validators=[DataRequired(), Length(max=100)],
                             default='نظام مراقبة الكاميرات',
                             render_kw={'class': 'form-control'})
    
    system_description = TextAreaField('وصف النظام', validators=[Optional(), Length(max=500)],
                                     render_kw={'class': 'form-control', 'rows': 3})
    
    # إعدادات الأمان
    session_timeout = IntegerField('انتهاء الجلسة (دقيقة)', 
                                  validators=[DataRequired(), NumberRange(min=5, max=1440)],
                                  default=60,
                                  render_kw={'class': 'form-control'})
    
    max_login_attempts = IntegerField('عدد محاولات تسجيل الدخول',
                                    validators=[DataRequired(), NumberRange(min=3, max=10)],
                                    default=5,
                                    render_kw={'class': 'form-control'})
    
    lockout_duration = IntegerField('مدة القفل (دقيقة)',
                                   validators=[DataRequired(), NumberRange(min=5, max=120)],
                                   default=30,
                                   render_kw={'class': 'form-control'})
    
    # إعدادات التسجيل
    default_recording_quality = SelectField('جودة التسجيل الافتراضية',
                                          validators=[DataRequired()],
                                          choices=[
                                              ('low', 'منخفضة'),
                                              ('medium', 'متوسطة'),
                                              ('high', 'عالية')
                                          ],
                                          default='medium',
                                          render_kw={'class': 'form-select'})
    
    max_recording_duration = IntegerField('أقصى مدة تسجيل (ساعة)',
                                        validators=[DataRequired(), NumberRange(min=1, max=24)],
                                        default=2,
                                        render_kw={'class': 'form-control'})
    
    auto_cleanup_enabled = BooleanField('تنظيف تلقائي للتسجيلات القديمة',
                                      default=True,
                                      render_kw={'class': 'form-check-input'})
    
    cleanup_after_days = IntegerField('حذف التسجيلات بعد (يوم)',
                                    validators=[Optional(), NumberRange(min=7, max=365)],
                                    default=30,
                                    render_kw={'class': 'form-control'})
    
    # إعدادات التنبيهات
    email_notifications = BooleanField('تفعيل إشعارات البريد الإلكتروني',
                                     default=True,
                                     render_kw={'class': 'form-check-input'})
    
    smtp_server = StringField('خادم SMTP', validators=[Optional(), Length(max=100)],
                             render_kw={'class': 'form-control'})
    
    smtp_port = IntegerField('منفذ SMTP', validators=[Optional(), NumberRange(min=1, max=65535)],
                           default=587,
                           render_kw={'class': 'form-control'})
    
    smtp_username = StringField('اسم مستخدم SMTP', validators=[Optional(), Length(max=100)],
                               render_kw={'class': 'form-control'})
    
    smtp_password = PasswordField('كلمة مرور SMTP', validators=[Optional()],
                                 render_kw={'class': 'form-control'})
    
    # إعدادات التخزين
    max_storage_gb = FloatField('أقصى مساحة تخزين (جيجابايت)',
                               validators=[DataRequired(), NumberRange(min=1, max=10000)],
                               default=100,
                               render_kw={'class': 'form-control'})
    
    storage_warning_threshold = IntegerField('تحذير عند امتلاء التخزين (%)',
                                           validators=[DataRequired(), NumberRange(min=50, max=95)],
                                           default=80,
                                           render_kw={'class': 'form-control'})
    
    submit = SubmitField('حفظ الإعدادات', render_kw={'class': 'btn btn-primary'})

class UserForm(FlaskForm):
    """نموذج إضافة مستخدم جديد"""
    
    # معلومات أساسية
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=20)],
                          render_kw={'class': 'form-control'})
    
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'class': 'form-control'})
    
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)],
                           render_kw={'class': 'form-control'})
    
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)],
                       render_kw={'class': 'form-control'})
    
    # كلمة المرور
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={'class': 'form-control'})
    
    password2 = PasswordField('تأكيد كلمة المرور',
                            validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')],
                            render_kw={'class': 'form-control'})
    
    # الصلاحيات
    is_admin = BooleanField('مدير النظام', default=False,
                          render_kw={'class': 'form-check-input'})
    
    is_active = BooleanField('حساب نشط', default=True,
                           render_kw={'class': 'form-check-input'})
    
    can_view_cameras = BooleanField('مشاهدة الكاميرات', default=True,
                                  render_kw={'class': 'form-check-input'})
    
    can_control_cameras = BooleanField('التحكم في الكاميرات', default=False,
                                     render_kw={'class': 'form-check-input'})
    
    can_manage_recordings = BooleanField('إدارة التسجيلات', default=False,
                                       render_kw={'class': 'form-check-input'})
    
    can_manage_users = BooleanField('إدارة المستخدمين', default=False,
                                  render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('إنشاء المستخدم', render_kw={'class': 'btn btn-primary'})
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم مستخدم بالفعل')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل')

class UserEditForm(FlaskForm):
    """نموذج تعديل مستخدم"""
    
    # معلومات أساسية
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=20)],
                          render_kw={'class': 'form-control'})
    
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'class': 'form-control'})
    
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)],
                           render_kw={'class': 'form-control'})
    
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)],
                       render_kw={'class': 'form-control'})
    
    # كلمة المرور (اختيارية للتعديل)
    password = PasswordField('كلمة المرور الجديدة', validators=[Optional(), Length(min=6)],
                           render_kw={'class': 'form-control'})
    
    password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                            validators=[EqualTo('password', message='كلمات المرور غير متطابقة')],
                            render_kw={'class': 'form-control'})
    
    # الصلاحيات
    is_admin = BooleanField('مدير النظام', render_kw={'class': 'form-check-input'})
    
    is_active = BooleanField('حساب نشط', render_kw={'class': 'form-check-input'})
    
    can_view_cameras = BooleanField('مشاهدة الكاميرات', render_kw={'class': 'form-check-input'})
    
    can_control_cameras = BooleanField('التحكم في الكاميرات', render_kw={'class': 'form-check-input'})
    
    can_manage_recordings = BooleanField('إدارة التسجيلات', render_kw={'class': 'form-check-input'})
    
    can_manage_users = BooleanField('إدارة المستخدمين', render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('حفظ التغييرات', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, original_username, original_email, *args, **kwargs):
        super(UserEditForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email
    
    def validate_username(self, username):
        if username.data != self.original_username:
            user = User.query.filter_by(username=username.data).first()
            if user is not None:
                raise ValidationError('اسم المستخدم مستخدم بالفعل')
    
    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('البريد الإلكتروني مستخدم بالفعل')

class BackupForm(FlaskForm):
    """نموذج النسخ الاحتياطي"""
    
    backup_type = SelectField('نوع النسخة الاحتياطية',
                             validators=[DataRequired()],
                             choices=[
                                 ('full', 'نسخة كاملة'),
                                 ('database', 'قاعدة البيانات فقط'),
                                 ('config', 'الإعدادات فقط')
                             ],
                             render_kw={'class': 'form-select'})
    
    include_recordings = BooleanField('تضمين التسجيلات', default=False,
                                    render_kw={'class': 'form-check-input'})
    
    compression_level = SelectField('مستوى الضغط',
                                   validators=[DataRequired()],
                                   choices=[
                                       ('0', 'بدون ضغط'),
                                       ('6', 'ضغط متوسط'),
                                       ('9', 'ضغط عالي')
                                   ],
                                   default='6',
                                   render_kw={'class': 'form-select'})
    
    submit = SubmitField('إنشاء نسخة احتياطية', render_kw={'class': 'btn btn-success'})

class MaintenanceForm(FlaskForm):
    """نموذج صيانة النظام"""
    
    # تنظيف التسجيلات
    cleanup_recordings = BooleanField('تنظيف التسجيلات القديمة',
                                    render_kw={'class': 'form-check-input'})
    
    cleanup_days = IntegerField('حذف التسجيلات الأقدم من (يوم)',
                               validators=[Optional(), NumberRange(min=1, max=365)],
                               default=30,
                               render_kw={'class': 'form-control'})
    
    delete_files = BooleanField('حذف الملفات من القرص', default=True,
                              render_kw={'class': 'form-check-input'})
    
    # تنظيف السجلات
    cleanup_logs = BooleanField('تنظيف سجلات النظام',
                              render_kw={'class': 'form-check-input'})
    
    log_retention_days = IntegerField('الاحتفاظ بالسجلات لـ (يوم)',
                                    validators=[Optional(), NumberRange(min=7, max=90)],
                                    default=30,
                                    render_kw={'class': 'form-control'})
    
    # تحسين قاعدة البيانات
    optimize_database = BooleanField('تحسين قاعدة البيانات',
                                   render_kw={'class': 'form-check-input'})
    
    # إعادة تشغيل الخدمات
    restart_services = BooleanField('إعادة تشغيل خدمات النظام',
                                  render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('تنفيذ الصيانة', render_kw={'class': 'btn btn-warning'})

class EmailSettingsForm(FlaskForm):
    """نموذج إعدادات البريد الإلكتروني"""
    
    smtp_enabled = BooleanField('تفعيل إشعارات البريد الإلكتروني',
                              render_kw={'class': 'form-check-input'})
    
    smtp_server = StringField('خادم SMTP', validators=[DataRequired(), Length(max=100)],
                             render_kw={'class': 'form-control'})
    
    smtp_port = IntegerField('المنفذ', validators=[DataRequired(), NumberRange(min=1, max=65535)],
                           default=587,
                           render_kw={'class': 'form-control'})
    
    smtp_security = SelectField('نوع الأمان',
                               validators=[DataRequired()],
                               choices=[
                                   ('none', 'بدون تشفير'),
                                   ('tls', 'TLS'),
                                   ('ssl', 'SSL')
                               ],
                               default='tls',
                               render_kw={'class': 'form-select'})
    
    smtp_username = StringField('اسم المستخدم', validators=[DataRequired(), Length(max=100)],
                               render_kw={'class': 'form-control'})
    
    smtp_password = PasswordField('كلمة المرور', validators=[DataRequired()],
                                 render_kw={'class': 'form-control'})
    
    from_email = StringField('البريد المرسل', validators=[DataRequired(), Email()],
                           render_kw={'class': 'form-control'})
    
    from_name = StringField('اسم المرسل', validators=[DataRequired(), Length(max=100)],
                          default='نظام مراقبة الكاميرات',
                          render_kw={'class': 'form-control'})
    
    # إعدادات التنبيهات
    alert_motion = BooleanField('تنبيه كشف الحركة', default=True,
                              render_kw={'class': 'form-check-input'})
    
    alert_connection = BooleanField('تنبيه انقطاع الاتصال', default=True,
                                  render_kw={'class': 'form-check-input'})
    
    alert_storage = BooleanField('تنبيه امتلاء التخزين', default=True,
                               render_kw={'class': 'form-check-input'})
    
    alert_system = BooleanField('تنبيهات النظام', default=True,
                              render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('حفظ إعدادات البريد', render_kw={'class': 'btn btn-primary'})
    
    test_email = SubmitField('اختبار البريد', render_kw={'class': 'btn btn-outline-secondary'})
