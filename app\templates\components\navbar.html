<!-- شريط التنقل الرئيسي -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container-fluid">
        
        <!-- شعار النظام -->
        <a class="navbar-brand d-flex align-items-center" href="{{ url_for('dashboard.index') }}">
            <i class="fas fa-video me-2"></i>
            <span class="fw-bold">نظام المراقبة</span>
        </a>
        
        <!-- زر القائمة للشاشات الصغيرة -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- عناصر التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            
            <!-- القائمة الرئيسية -->
            <ul class="navbar-nav me-auto">
                
                <!-- لوحة التحكم -->
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'dashboard.index' }}" 
                       href="{{ url_for('dashboard.index') }}">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                </li>
                
                <!-- الكاميرات -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ 'active' if 'cameras' in request.endpoint }}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-camera me-1"></i>
                        الكاميرات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('cameras.index') }}">
                            <i class="fas fa-list me-2"></i>قائمة الكاميرات</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('cameras.add') }}">
                            <i class="fas fa-plus me-2"></i>إضافة كاميرا</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('cameras.live_view') }}">
                            <i class="fas fa-eye me-2"></i>المشاهدة المباشرة</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('cameras.multi_view') }}">
                            <i class="fas fa-th me-2"></i>عرض متعدد</a></li>
                    </ul>
                </li>
                
                <!-- التسجيلات -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ 'active' if 'recordings' in request.endpoint }}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-video me-1"></i>
                        التسجيلات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('recordings.index') }}">
                            <i class="fas fa-list me-2"></i>جميع التسجيلات</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('recordings.search') }}">
                            <i class="fas fa-search me-2"></i>البحث في التسجيلات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('recordings.schedule') }}">
                            <i class="fas fa-calendar me-2"></i>جدولة التسجيل</a></li>
                    </ul>
                </li>
                
                <!-- التنبيهات -->
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if 'alerts' in request.endpoint }} position-relative" 
                       href="{{ url_for('dashboard.alerts') }}">
                        <i class="fas fa-bell me-1"></i>
                        التنبيهات
                        <span class="badge bg-danger rounded-pill position-absolute top-0 start-100 translate-middle" 
                              id="alertsCount" style="font-size: 0.7em;">0</span>
                    </a>
                </li>
                
            </ul>
            
            <!-- القائمة اليمنى -->
            <ul class="navbar-nav">
                
                <!-- حالة النظام -->
                <li class="nav-item">
                    <span class="navbar-text me-3">
                        <i class="fas fa-circle text-success me-1" id="systemStatus"></i>
                        <span id="systemStatusText">متصل</span>
                    </span>
                </li>
                
                <!-- تغيير اللغة -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        {{ 'العربية' if session.get('language', 'ar') == 'ar' else 'English' }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('main.set_language', language='ar') }}">
                            <i class="fas fa-flag me-2"></i>العربية</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.set_language', language='en') }}">
                            <i class="fas fa-flag me-2"></i>English</a></li>
                    </ul>
                </li>
                
                <!-- قائمة المستخدم -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2 fs-5"></i>
                        <span>{{ current_user.full_name or current_user.username }}</span>
                        {% if current_user.is_admin %}
                            <span class="badge bg-warning text-dark ms-2">مدير</span>
                        {% endif %}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            <i class="fas fa-user me-2"></i>{{ current_user.username }}
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        
                        <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        
                        <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور</a></li>
                        
                        {% if current_user.is_admin %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('settings.index') }}">
                                <i class="fas fa-cog me-2"></i>إعدادات النظام</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('settings.users') }}">
                                <i class="fas fa-users me-2"></i>إدارة المستخدمين</a></li>
                        {% endif %}
                        
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </li>
                
            </ul>
            
        </div>
    </div>
</nav>

<!-- مساحة إضافية لتعويض الشريط الثابت -->
<div style="height: 70px;"></div>
