# -*- coding: utf-8 -*-
"""
نموذج الكاميرا
Camera Model
"""

from datetime import datetime
from app import db

class Camera(db.Model):
    """نموذج الكاميرا"""
    
    __tablename__ = 'cameras'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # معلومات الاتصال
    ip_address = db.Column(db.String(45), nullable=False)  # IPv4 or IPv6
    port = db.Column(db.Integer, default=554)
    username = db.Column(db.String(50))
    password = db.Column(db.String(100))
    
    # نوع الكاميرا والبروتوكول
    camera_type = db.Column(db.String(20), nullable=False)  # IP, DVR, NVR
    protocol = db.Column(db.String(20), default='RTSP')  # RTSP, HTTP, ONVIF
    brand = db.Column(db.String(50))  # Hikvision, Dahua, Uniview, etc.
    model = db.Column(db.String(100))
    
    # إعدادات البث
    rtsp_url = db.Column(db.String(500))
    http_url = db.Column(db.String(500))
    onvif_url = db.Column(db.String(500))
    stream_profile = db.Column(db.String(20), default='main')  # main, sub
    
    # القنوات (للـ DVR/NVR)
    channel_number = db.Column(db.Integer, default=1)
    total_channels = db.Column(db.Integer, default=1)
    
    # الموقع
    location = db.Column(db.String(200))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    
    # الحالة والإعدادات
    is_active = db.Column(db.Boolean, default=True)
    is_public = db.Column(db.Boolean, default=False)
    is_recording = db.Column(db.Boolean, default=False)
    motion_detection = db.Column(db.Boolean, default=False)
    
    # إعدادات التسجيل
    recording_enabled = db.Column(db.Boolean, default=True)
    recording_schedule = db.Column(db.String(500))  # JSON format
    recording_quality = db.Column(db.String(20), default='medium')
    
    # إعدادات كشف الحركة
    motion_sensitivity = db.Column(db.Integer, default=50)
    motion_threshold = db.Column(db.Integer, default=1000)
    motion_areas = db.Column(db.Text)  # JSON format for detection areas
    
    # حالة الاتصال
    connection_status = db.Column(db.String(20), default='disconnected')
    last_seen = db.Column(db.DateTime)
    connection_attempts = db.Column(db.Integer, default=0)
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    recordings = db.relationship('Recording', backref='camera', lazy='dynamic', cascade='all, delete-orphan')
    alerts = db.relationship('Alert', backref='camera', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_stream_url(self, profile='main'):
        """الحصول على رابط البث"""
        if self.protocol == 'RTSP' and self.rtsp_url:
            return self.rtsp_url
        elif self.protocol == 'HTTP' and self.http_url:
            return self.http_url
        elif self.protocol == 'ONVIF' and self.onvif_url:
            return self.onvif_url
        else:
            # إنشاء رابط افتراضي
            if self.protocol == 'RTSP':
                return f"rtsp://{self.username}:{self.password}@{self.ip_address}:{self.port}/cam/realmonitor?channel={self.channel_number}&subtype=0"
            elif self.protocol == 'HTTP':
                return f"http://{self.username}:{self.password}@{self.ip_address}:{self.port}/mjpeg/{self.channel_number}"
        
        return None
    
    def is_online(self):
        """التحقق من حالة الاتصال"""
        return self.connection_status == 'connected'
    
    def update_connection_status(self, status):
        """تحديث حالة الاتصال"""
        self.connection_status = status
        if status == 'connected':
            self.last_seen = datetime.utcnow()
            self.connection_attempts = 0
        else:
            self.connection_attempts += 1
        db.session.commit()
    
    def get_recording_schedule(self):
        """الحصول على جدول التسجيل"""
        import json
        if self.recording_schedule:
            try:
                return json.loads(self.recording_schedule)
            except:
                pass
        return {}
    
    def set_recording_schedule(self, schedule):
        """تعيين جدول التسجيل"""
        import json
        self.recording_schedule = json.dumps(schedule)
    
    def get_motion_areas(self):
        """الحصول على مناطق كشف الحركة"""
        import json
        if self.motion_areas:
            try:
                return json.loads(self.motion_areas)
            except:
                pass
        return []
    
    def set_motion_areas(self, areas):
        """تعيين مناطق كشف الحركة"""
        import json
        self.motion_areas = json.dumps(areas)
    
    def __repr__(self):
        return f'<Camera {self.name} ({self.ip_address})>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'ip_address': self.ip_address,
            'port': self.port,
            'camera_type': self.camera_type,
            'protocol': self.protocol,
            'brand': self.brand,
            'model': self.model,
            'channel_number': self.channel_number,
            'total_channels': self.total_channels,
            'location': self.location,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'is_active': self.is_active,
            'is_public': self.is_public,
            'is_recording': self.is_recording,
            'motion_detection': self.motion_detection,
            'recording_enabled': self.recording_enabled,
            'recording_quality': self.recording_quality,
            'motion_sensitivity': self.motion_sensitivity,
            'connection_status': self.connection_status,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'created_at': self.created_at.isoformat()
        }
