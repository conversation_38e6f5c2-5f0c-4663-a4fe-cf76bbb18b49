# -*- coding: utf-8 -*-
"""
مسارات المصادقة
Authentication Routes
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.urls import url_parse
from datetime import datetime

from app import db
from app.models import User
from app.forms.auth import LoginForm, RegisterForm, ChangePasswordForm

bp = Blueprint('auth', __name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user is None:
            flash('اسم المستخدم غير صحيح', 'error')
            return render_template('auth/login.html', form=form)
        
        # التحقق من حالة القفل
        if user.is_locked():
            flash('الحساب مقفل مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة', 'error')
            return render_template('auth/login.html', form=form)
        
        # التحقق من حالة النشاط
        if not user.is_active:
            flash('الحساب غير نشط، يرجى الاتصال بالمدير', 'error')
            return render_template('auth/login.html', form=form)
        
        # التحقق من كلمة المرور
        if not user.check_password(form.password.data):
            user.increment_failed_attempts()
            flash('كلمة المرور غير صحيحة', 'error')
            return render_template('auth/login.html', form=form)
        
        # تسجيل دخول ناجح
        user.reset_failed_attempts()
        login_user(user, remember=form.remember_me.data)
        
        flash(f'مرحباً {user.username}!', 'success')
        
        # إعادة التوجيه إلى الصفحة المطلوبة أو لوحة التحكم
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('dashboard.index')
        
        return redirect(next_page)
    
    return render_template('auth/login.html', form=form)

@bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    username = current_user.username
    logout_user()
    flash(f'تم تسجيل خروج {username} بنجاح', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """تسجيل مستخدم جديد"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    # التحقق من السماح بالتسجيل (يمكن تعطيله في الإنتاج)
    if not current_user.is_authenticated:
        # في الإنتاج، قد نريد السماح بالتسجيل فقط للمديرين
        pass
    
    form = RegisterForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', form=form)

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html', form=form)
        
        current_user.set_password(form.new_password.data)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('dashboard.index'))
    
    return render_template('auth/change_password.html', form=form)

@bp.route('/profile')
@login_required
def profile():
    """الملف الشخصي"""
    return render_template('auth/profile.html', user=current_user)

@bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """تعديل الملف الشخصي"""
    from app.forms.auth import EditProfileForm
    
    form = EditProfileForm(obj=current_user)
    
    if form.validate_on_submit():
        current_user.full_name = form.full_name.data
        current_user.email = form.email.data
        current_user.phone = form.phone.data
        
        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/edit_profile.html', form=form)

@bp.route('/unlock-account/<int:user_id>')
@login_required
def unlock_account(user_id):
    """إلغاء قفل حساب (للمديرين فقط)"""
    if not current_user.is_admin:
        flash('غير مسموح لك بهذا الإجراء', 'error')
        return redirect(url_for('dashboard.index'))
    
    user = User.query.get_or_404(user_id)
    user.unlock_account()
    
    flash(f'تم إلغاء قفل حساب {user.username}', 'success')
    return redirect(url_for('settings.users'))

@bp.before_request
def before_request():
    """قبل كل طلب"""
    if current_user.is_authenticated:
        # تحديث آخر نشاط
        current_user.last_login = datetime.utcnow()
        db.session.commit()
