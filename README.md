# نظام مراقبة الكاميرات الاحترافي
## Professional Surveillance Camera System

نظام شامل لإدارة ومراقبة الكاميرات الأمنية مع دعم كامل للغة العربية وواجهة RTL.

---

## 🌟 المميزات الرئيسية

### 📹 دعم أنواع متعددة من الكاميرات
- **كاميرات IP** (RTSP, HTTP, MJPEG)
- **أجهزة DVR/NVR** (Dahua, Hikvision, Uniview)
- **بروتوكول ONVIF** للاتصال التلقائي
- **دعم P2P** للكاميرات المتوافقة

### 🎥 البث المباشر والتسجيل
- بث مباشر عالي الجودة
- تسجيل تلقائي ومجدول
- كشف الحركة الذكي
- التقاط الصور التلقائي
- أرشفة منظمة للتسجيلات

### 🌐 واجهة ويب احترافية
- **دعم كامل للغة العربية** مع تخطيط RTL
- واجهة مستخدم حديثة وسهلة الاستخدام
- لوحة تحكم شاملة مع الإحصائيات
- عرض متعدد الكاميرات
- دعم الشاشات المختلفة (موبايل، تابلت، سطح المكتب)

### 🔔 نظام التنبيهات الذكي
- كشف الحركة المتقدم
- تنبيهات انقطاع الاتصال
- إشعارات البريد الإلكتروني
- تصنيف التنبيهات حسب الأولوية

### 👥 إدارة المستخدمين والصلاحيات
- نظام مصادقة آمن
- صلاحيات متدرجة للمستخدمين
- تسجيل العمليات والأنشطة
- حماية من الهجمات الأمنية

### 🗺️ الخرائط والمواقع (اختياري)
- عرض مواقع الكاميرات على الخريطة
- تكامل مع Google Maps أو Leaflet
- النقر على الموقع لعرض البث

---

## 🛠️ التقنيات المستخدمة

### Backend
- **Flask** - إطار العمل الرئيسي
- **SQLAlchemy** - قاعدة البيانات
- **OpenCV** - معالجة الفيديو والصور
- **FFmpeg** - تحويل وضغط الفيديو
- **ONVIF-Zeep** - دعم بروتوكول ONVIF

### Frontend
- **Bootstrap 5 RTL** - التصميم والتخطيط
- **JavaScript/jQuery** - التفاعل والديناميكية
- **Font Awesome** - الأيقونات
- **Cairo Font** - الخط العربي

### Database
- **SQLite** (للتطوير)
- **PostgreSQL** (للإنتاج)

---

## 📋 متطلبات النظام

### متطلبات البرمجيات
- Python 3.8 أو أحدث
- FFmpeg
- OpenCV
- متصفح ويب حديث

### متطلبات الأجهزة (الحد الأدنى)
- معالج: Intel i3 أو AMD Ryzen 3
- ذاكرة: 4GB RAM
- تخزين: 100GB مساحة فارغة
- شبكة: اتصال إنترنت مستقر

### متطلبات الأجهزة (المُوصى بها)
- معالج: Intel i5 أو AMD Ryzen 5
- ذاكرة: 8GB RAM أو أكثر
- تخزين: 500GB SSD
- شبكة: اتصال جيجابت

---

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/surveillance-system.git
cd surveillance-system
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### 5. تشغيل النظام
```bash
python run.py
```

### 6. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

**بيانات الدخول الافتراضية:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

---

## ⚙️ الإعدادات

### إعدادات قاعدة البيانات
```python
# في ملف config/config.py
SQLALCHEMY_DATABASE_URI = 'postgresql://user:password@localhost/surveillance_db'
```

### إعدادات البريد الإلكتروني
```python
SMTP_SERVER = 'smtp.gmail.com'
SMTP_PORT = 587
SMTP_USERNAME = '<EMAIL>'
SMTP_PASSWORD = 'your-app-password'
```

### إعدادات التسجيل
```python
RECORDINGS_FOLDER = '/path/to/recordings'
SNAPSHOTS_FOLDER = '/path/to/snapshots'
MAX_RECORDING_SIZE = 1024 * 1024 * 1024  # 1GB
```

---

## 📖 دليل الاستخدام

### إضافة كاميرا جديدة
1. انتقل إلى **الكاميرات** > **إضافة كاميرا**
2. أدخل معلومات الكاميرا:
   - الاسم والوصف
   - عنوان IP والمنفذ
   - اسم المستخدم وكلمة المرور
   - نوع الكاميرا والبروتوكول
3. اختبر الاتصال
4. احفظ الإعدادات

### مشاهدة البث المباشر
1. انتقل إلى **الكاميرات** > **المشاهدة المباشرة**
2. اختر الكاميرا المطلوبة
3. استخدم أدوات التحكم:
   - تكبير/تصغير
   - ملء الشاشة
   - التقاط صورة
   - بدء/إيقاف التسجيل

### إدارة التسجيلات
1. انتقل إلى **التسجيلات**
2. استعرض التسجيلات حسب:
   - التاريخ والوقت
   - الكاميرا
   - نوع التسجيل
3. شاهد أو حمل التسجيلات
4. احذف التسجيلات غير المرغوبة

### إعداد كشف الحركة
1. انتقل إلى إعدادات الكاميرا
2. فعّل **كشف الحركة**
3. اضبط الحساسية والعتبة
4. حدد مناطق الكشف
5. اختر إجراءات التنبيه

---

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال بالكاميرات
- تأكد من صحة عنوان IP والمنفذ
- تحقق من اسم المستخدم وكلمة المرور
- تأكد من أن الكاميرا تدعم البروتوكول المحدد
- فحص إعدادات الشبكة والجدار الناري

### مشاكل البث المباشر
- تحقق من سرعة الإنترنت
- قلل من جودة البث إذا لزم الأمر
- أعد تشغيل خدمة البث
- تأكد من توفر مساحة كافية للتخزين

### مشاكل الأداء
- راقب استهلاك المعالج والذاكرة
- قلل من عدد الكاميرات المتصلة في نفس الوقت
- استخدم قاعدة بيانات PostgreSQL للإنتاج
- فعّل التخزين المؤقت

---

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs.surveillance-system.com](https://docs.surveillance-system.com)
- **المجتمع**: [community.surveillance-system.com](https://community.surveillance-system.com)

---

## 🙏 شكر وتقدير

- فريق OpenCV لمكتبة معالجة الصور
- مجتمع Flask للإطار الرائع
- مطوري Bootstrap لواجهة المستخدم
- جميع المساهمين في المشروع

---

**تم تطوير هذا النظام بعناية لخدمة المجتمع العربي والعالمي** 🌍
