#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لنظام مراقبة الكاميرات
Simple Run for Surveillance Camera System
"""

import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    from app.models import User
    
    print("🚀 بدء تشغيل نظام مراقبة الكاميرات...")
    
    # إنشاء التطبيق
    app = create_app()
    
    # إنشاء قاعدة البيانات والمستخدم الافتراضي
    with app.app_context():
        print("💾 إنشاء قاعدة البيانات...")
        db.create_all()
        
        # إنشاء المستخدم الافتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                is_admin=True,
                is_active=True,
                can_view_cameras=True,
                can_control_cameras=True,
                can_manage_recordings=True,
                can_manage_users=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("👤 تم إنشاء المستخدم الافتراضي")
        
        print("✅ تم إعداد قاعدة البيانات بنجاح")
    
    print("\n" + "="*50)
    print("🎉 نظام مراقبة الكاميرات جاهز للتشغيل!")
    print("="*50)
    print("📱 الواجهة متاحة على: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("="*50)
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("="*50)
    
    # تشغيل الخادم
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login flask-wtf")
    
except Exception as e:
    print(f"❌ خطأ في التشغيل: {e}")
    import traceback
    traceback.print_exc()
