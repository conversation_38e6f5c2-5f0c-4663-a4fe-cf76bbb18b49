<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>
        {% if title %}
            {{ title }} - نظام مراقبة الكاميرات
        {% else %}
            نظام مراقبة الكاميرات الاحترافي
        {% endif %}
    </title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    {% block head %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
        {% include 'components/navbar.html' %}
    {% endif %}
    
    <!-- Main Content -->
    <main class="{% if current_user.is_authenticated %}main-content{% else %}auth-main{% endif %}">
        
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid mt-3">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
        
    </main>
    
    <!-- Footer -->
    {% if not current_user.is_authenticated %}
        {% include 'components/footer.html' %}
    {% endif %}
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-3">جاري التحميل...</div>
        </div>
    </div>
    
    <!-- Scripts -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
    
    <script>
        // إعدادات عامة للتطبيق
        window.APP_CONFIG = {
            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},
            currentUser: {{ current_user.to_dict() | tojson if current_user.is_authenticated else 'null' }},
            language: '{{ session.get("language", "ar") }}',
            csrfToken: '{{ csrf_token() if csrf_token else "" }}'
        };
        
        // تهيئة التطبيق
        $(document).ready(function() {
            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تفعيل popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // إخفاء رسائل التنبيه تلقائياً
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
        
        // دوال مساعدة
        function showLoading() {
            $('#loadingOverlay').removeClass('d-none');
        }
        
        function hideLoading() {
            $('#loadingOverlay').addClass('d-none');
        }
        
        function showToast(message, type = 'info') {
            // يمكن إضافة نظام toast هنا
            console.log(`${type}: ${message}`);
        }
    </script>
    
</body>
</html>
