# -*- coding: utf-8 -*-
"""
نموذج التنبيهات
Alert Model
"""

from datetime import datetime
from app import db

class Alert(db.Model):
    """نموذج التنبيهات"""
    
    __tablename__ = 'alerts'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text)
    
    # نوع التنبيه
    alert_type = db.Column(db.String(50), nullable=False)  # motion, connection, system, security
    severity = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    
    # الحالة
    status = db.Column(db.String(20), default='new')  # new, acknowledged, resolved, dismissed
    is_read = db.Column(db.Bo<PERSON>an, default=False)
    
    # معلومات إضافية
    metadata = db.Column(db.Text)  # JSON format for additional data
    image_path = db.Column(db.String(500))  # مسار صورة التنبيه
    video_path = db.Column(db.String(500))  # مسار فيديو التنبيه
    
    # الإجراءات
    action_taken = db.Column(db.String(200))
    auto_resolved = db.Column(db.Boolean, default=False)
    
    # التواريخ
    triggered_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    acknowledged_at = db.Column(db.DateTime)
    resolved_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات الخارجية
    camera_id = db.Column(db.Integer, db.ForeignKey('cameras.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # المستخدم الذي تعامل مع التنبيه
    
    # الفهارس
    __table_args__ = (
        db.Index('idx_alert_camera_date', 'camera_id', 'triggered_at'),
        db.Index('idx_alert_type_status', 'alert_type', 'status'),
        db.Index('idx_alert_severity', 'severity'),
    )
    
    def get_metadata(self):
        """الحصول على البيانات الإضافية"""
        import json
        if self.metadata:
            try:
                return json.loads(self.metadata)
            except:
                pass
        return {}
    
    def set_metadata(self, data):
        """تعيين البيانات الإضافية"""
        import json
        self.metadata = json.dumps(data)
    
    def acknowledge(self, user_id=None):
        """الإقرار بالتنبيه"""
        self.status = 'acknowledged'
        self.is_read = True
        self.acknowledged_at = datetime.utcnow()
        if user_id:
            self.user_id = user_id
        db.session.commit()
    
    def resolve(self, user_id=None, action=None):
        """حل التنبيه"""
        self.status = 'resolved'
        self.resolved_at = datetime.utcnow()
        if user_id:
            self.user_id = user_id
        if action:
            self.action_taken = action
        db.session.commit()
    
    def dismiss(self, user_id=None):
        """تجاهل التنبيه"""
        self.status = 'dismissed'
        self.is_read = True
        if user_id:
            self.user_id = user_id
        db.session.commit()
    
    def get_severity_color(self):
        """الحصول على لون الخطورة"""
        colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'dark'
        }
        return colors.get(self.severity, 'secondary')
    
    def get_type_icon(self):
        """الحصول على أيقونة نوع التنبيه"""
        icons = {
            'motion': 'fas fa-running',
            'connection': 'fas fa-wifi',
            'system': 'fas fa-cog',
            'security': 'fas fa-shield-alt',
            'recording': 'fas fa-video',
            'storage': 'fas fa-hdd'
        }
        return icons.get(self.alert_type, 'fas fa-bell')
    
    def get_age_minutes(self):
        """الحصول على عمر التنبيه بالدقائق"""
        return int((datetime.utcnow() - self.triggered_at).total_seconds() / 60)
    
    def get_age_formatted(self):
        """الحصول على عمر التنبيه منسق"""
        age_minutes = self.get_age_minutes()
        
        if age_minutes < 1:
            return "الآن"
        elif age_minutes < 60:
            return f"منذ {age_minutes} دقيقة"
        elif age_minutes < 1440:  # 24 hours
            hours = age_minutes // 60
            return f"منذ {hours} ساعة"
        else:
            days = age_minutes // 1440
            return f"منذ {days} يوم"
    
    def has_media(self):
        """التحقق من وجود وسائط مرفقة"""
        return bool(self.image_path or self.video_path)
    
    @staticmethod
    def create_motion_alert(camera, image_path=None):
        """إنشاء تنبيه كشف حركة"""
        alert = Alert(
            title=f"كشف حركة - {camera.name}",
            message=f"تم اكتشاف حركة في الكاميرا {camera.name} الموجودة في {camera.location or 'موقع غير محدد'}",
            alert_type='motion',
            severity='medium',
            camera_id=camera.id,
            image_path=image_path
        )
        db.session.add(alert)
        db.session.commit()
        return alert
    
    @staticmethod
    def create_connection_alert(camera, is_connected=False):
        """إنشاء تنبيه اتصال"""
        if is_connected:
            title = f"استعادة الاتصال - {camera.name}"
            message = f"تم استعادة الاتصال مع الكاميرا {camera.name}"
            severity = 'low'
        else:
            title = f"انقطاع الاتصال - {camera.name}"
            message = f"انقطع الاتصال مع الكاميرا {camera.name}"
            severity = 'high'
        
        alert = Alert(
            title=title,
            message=message,
            alert_type='connection',
            severity=severity,
            camera_id=camera.id
        )
        db.session.add(alert)
        db.session.commit()
        return alert
    
    def __repr__(self):
        return f'<Alert {self.title} - {self.alert_type}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'alert_type': self.alert_type,
            'severity': self.severity,
            'status': self.status,
            'is_read': self.is_read,
            'action_taken': self.action_taken,
            'auto_resolved': self.auto_resolved,
            'triggered_at': self.triggered_at.isoformat(),
            'acknowledged_at': self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'camera_id': self.camera_id,
            'camera_name': self.camera.name if self.camera else None,
            'user_id': self.user_id,
            'age_formatted': self.get_age_formatted(),
            'severity_color': self.get_severity_color(),
            'type_icon': self.get_type_icon(),
            'has_media': self.has_media(),
            'created_at': self.created_at.isoformat()
        }
