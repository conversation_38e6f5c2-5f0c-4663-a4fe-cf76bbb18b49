/* نظام مراقبة الكاميرات - الأنماط المخصصة */

/* الخطوط والإعدادات الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f6fa;
    font-size: 14px;
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    box-shadow: var(--box-shadow);
    border-bottom: 3px solid var(--secondary-color);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: var(--secondary-color);
    color: white !important;
}

/* المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - 70px);
    padding: 20px 0;
}

.auth-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* صفحات المصادقة */
.auth-container {
    width: 100%;
    padding: 20px 0;
}

.auth-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95);
}

.auth-card .card-header {
    border: none;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) !important;
    padding: 2rem 1.5rem;
}

.auth-logo {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 12px 24px;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.btn-warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.btn-danger {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid var(--secondary-color);
    font-weight: 600;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transition: var(--transition);
}

.stats-card:hover::before {
    transform: scale(1.5);
}

.stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

.stats-label {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

/* جدول الكاميرات */
.camera-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table {
    margin: 0;
}

.table th {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #f1f3f4;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* حالة الكاميرا */
.camera-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.camera-status.online {
    background-color: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.camera-status.offline {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.camera-status.connecting {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

/* مشغل الفيديو */
.video-player {
    background: #000;
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    padding: 1rem;
    color: white;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    font-weight: 500;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
    color: #155724;
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
    color: #721c24;
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
    color: #856404;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

/* شاشة التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

/* الشاشات الصغيرة */
@media (max-width: 768px) {
    .main-content {
        padding: 10px 0;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .stats-icon {
        font-size: 2rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* تخصيصات إضافية للعربية */
.rtl-flip {
    transform: scaleX(-1);
}

.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تحسينات الطباعة */
@media print {
    .navbar, .btn, .loading-overlay {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
