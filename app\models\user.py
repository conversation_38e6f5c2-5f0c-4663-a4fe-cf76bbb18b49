# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # معلومات شخصية
    full_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    
    # الصلاحيات
    is_admin = db.Column(db.<PERSON>, default=False)
    is_active = db.Column(db.<PERSON>, default=True)
    can_view_cameras = db.Column(db.<PERSON>, default=True)
    can_control_cameras = db.Column(db.Boolean, default=False)
    can_manage_recordings = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    
    # إعدادات الأمان
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    last_login = db.Column(db.DateTime)
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    recordings = db.relationship('Recording', backref='user', lazy='dynamic')
    alerts = db.relationship('Alert', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def is_locked(self):
        """التحقق من حالة القفل"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False
    
    def lock_account(self, duration_minutes=30):
        """قفل الحساب"""
        from datetime import timedelta
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        db.session.commit()
    
    def unlock_account(self):
        """إلغاء قفل الحساب"""
        self.locked_until = None
        self.failed_login_attempts = 0
        db.session.commit()
    
    def increment_failed_attempts(self):
        """زيادة عدد محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.lock_account()
        db.session.commit()
    
    def reset_failed_attempts(self):
        """إعادة تعيين محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts = 0
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def has_permission(self, permission):
        """التحقق من الصلاحية"""
        if self.is_admin:
            return True
        return getattr(self, permission, False)
    
    def get_accessible_cameras(self):
        """الحصول على الكاميرات المتاحة للمستخدم"""
        from app.models.camera import Camera
        if self.is_admin:
            return Camera.query.all()
        # يمكن إضافة منطق أكثر تعقيداً هنا
        return Camera.query.filter_by(is_public=True).all()
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'phone': self.phone,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'can_view_cameras': self.can_view_cameras,
            'can_control_cameras': self.can_control_cameras,
            'can_manage_recordings': self.can_manage_recordings,
            'can_manage_users': self.can_manage_users,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat()
        }
