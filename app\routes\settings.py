# -*- coding: utf-8 -*-
"""
مسارات إعدادات النظام
System Settings Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime
from sqlalchemy import func

from app import db
from app.models import User, Camera, Recording, Alert
from app.forms.settings import SystemSettingsForm, UserForm, UserEditForm
from app.utils.camera_manager import camera_manager
from app.utils.recording_manager import recording_manager

bp = Blueprint('settings', __name__)

@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى إعدادات النظام', 'error')
        return redirect(url_for('dashboard.index'))
    
    # إحصائيات النظام
    system_stats = {
        'total_users': User.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'total_cameras': Camera.query.count(),
        'online_cameras': Camera.query.filter_by(connection_status='connected').count(),
        'total_recordings': Recording.query.count(),
        'total_alerts': Alert.query.count(),
        'unread_alerts': Alert.query.filter_by(is_read=False).count(),
        'storage_used': db.session.query(func.sum(Recording.file_size)).scalar() or 0
    }
    
    return render_template('settings/index.html', stats=system_stats)

@bp.route('/system', methods=['GET', 'POST'])
@login_required
def system():
    """إعدادات النظام العامة"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بتعديل إعدادات النظام', 'error')
        return redirect(url_for('settings.index'))
    
    form = SystemSettingsForm()
    
    if form.validate_on_submit():
        # حفظ الإعدادات في ملف التكوين أو قاعدة البيانات
        # هذا مثال بسيط، يمكن تطويره أكثر
        
        flash('تم حفظ إعدادات النظام بنجاح', 'success')
        return redirect(url_for('settings.system'))
    
    return render_template('settings/system.html', form=form)

@bp.route('/users')
@login_required
def users():
    """إدارة المستخدمين"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بإدارة المستخدمين', 'error')
        return redirect(url_for('settings.index'))
    
    page = request.args.get('page', 1, type=int)
    per_page = 15
    
    # فلاتر البحث
    search = request.args.get('search')
    status = request.args.get('status')
    role = request.args.get('role')
    
    query = User.query
    
    if search:
        query = query.filter(
            User.username.contains(search) |
            User.email.contains(search) |
            User.full_name.contains(search)
        )
    
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)
    elif status == 'locked':
        query = query.filter(User.locked_until.isnot(None))
    
    if role == 'admin':
        query = query.filter_by(is_admin=True)
    elif role == 'user':
        query = query.filter_by(is_admin=False)
    
    users_pagination = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات المستخدمين
    user_stats = {
        'total': User.query.count(),
        'active': User.query.filter_by(is_active=True).count(),
        'inactive': User.query.filter_by(is_active=False).count(),
        'admins': User.query.filter_by(is_admin=True).count(),
        'locked': User.query.filter(User.locked_until.isnot(None)).count()
    }
    
    return render_template('settings/users.html',
                         users=users_pagination.items,
                         pagination=users_pagination,
                         stats=user_stats,
                         current_filters={
                             'search': search,
                             'status': status,
                             'role': role
                         })

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بإضافة مستخدمين', 'error')
        return redirect(url_for('settings.users'))
    
    form = UserForm()
    
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            phone=form.phone.data,
            is_admin=form.is_admin.data,
            is_active=form.is_active.data,
            can_view_cameras=form.can_view_cameras.data,
            can_control_cameras=form.can_control_cameras.data,
            can_manage_recordings=form.can_manage_recordings.data,
            can_manage_users=form.can_manage_users.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash(f'تم إنشاء المستخدم {user.username} بنجاح', 'success')
        return redirect(url_for('settings.users'))
    
    return render_template('settings/add_user.html', form=form)

@bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    """تعديل مستخدم"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بتعديل المستخدمين', 'error')
        return redirect(url_for('settings.users'))
    
    user = User.query.get_or_404(user_id)
    
    # منع تعديل المدير الرئيسي
    if user.id == 1 and current_user.id != 1:
        flash('لا يمكن تعديل المدير الرئيسي', 'error')
        return redirect(url_for('settings.users'))
    
    form = UserEditForm(obj=user)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.phone = form.phone.data
        user.is_admin = form.is_admin.data
        user.is_active = form.is_active.data
        user.can_view_cameras = form.can_view_cameras.data
        user.can_control_cameras = form.can_control_cameras.data
        user.can_manage_recordings = form.can_manage_recordings.data
        user.can_manage_users = form.can_manage_users.data
        
        # تغيير كلمة المرور إذا تم إدخال واحدة جديدة
        if form.password.data:
            user.set_password(form.password.data)
        
        db.session.commit()
        
        flash(f'تم تحديث المستخدم {user.username} بنجاح', 'success')
        return redirect(url_for('settings.users'))
    
    return render_template('settings/edit_user.html', form=form, user=user)

@bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    """حذف مستخدم"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح لك بحذف المستخدمين'}), 403
    
    user = User.query.get_or_404(user_id)
    
    # منع حذف المدير الرئيسي أو النفس
    if user.id == 1 or user.id == current_user.id:
        return jsonify({'error': 'لا يمكن حذف هذا المستخدم'}), 400
    
    username = user.username
    db.session.delete(user)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'تم حذف المستخدم {username} بنجاح'
    })

@bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """تفعيل/إلغاء تفعيل مستخدم"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح لك بتعديل حالة المستخدمين'}), 403
    
    user = User.query.get_or_404(user_id)
    
    # منع تعديل المدير الرئيسي أو النفس
    if user.id == 1 or user.id == current_user.id:
        return jsonify({'error': 'لا يمكن تعديل حالة هذا المستخدم'}), 400
    
    user.is_active = not user.is_active
    db.session.commit()
    
    status = 'تفعيل' if user.is_active else 'إلغاء تفعيل'
    
    return jsonify({
        'success': True,
        'message': f'تم {status} المستخدم {user.username}',
        'is_active': user.is_active
    })

@bp.route('/backup')
@login_required
def backup():
    """النسخ الاحتياطي"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى النسخ الاحتياطي', 'error')
        return redirect(url_for('settings.index'))
    
    return render_template('settings/backup.html')

@bp.route('/backup/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح لك بإنشاء نسخ احتياطية'}), 403
    
    try:
        import sqlite3
        import shutil
        import zipfile
        from datetime import datetime
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)
        
        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'surveillance_backup_{timestamp}.zip'
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # إنشاء ملف ZIP
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # نسخ قاعدة البيانات
            if os.path.exists('surveillance.db'):
                zipf.write('surveillance.db', 'database/surveillance.db')
            
            # نسخ ملفات التكوين
            if os.path.exists('config'):
                for root, dirs, files in os.walk('config'):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, '.')
                        zipf.write(file_path, arcname)
        
        file_size = os.path.getsize(backup_path)
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'filename': backup_filename,
            'size': file_size
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        })

@bp.route('/logs')
@login_required
def logs():
    """عرض سجلات النظام"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بعرض سجلات النظام', 'error')
        return redirect(url_for('settings.index'))
    
    return render_template('settings/logs.html')

@bp.route('/maintenance')
@login_required
def maintenance():
    """صيانة النظام"""
    
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى صيانة النظام', 'error')
        return redirect(url_for('settings.index'))
    
    return render_template('settings/maintenance.html')

@bp.route('/maintenance/cleanup-recordings', methods=['POST'])
@login_required
def cleanup_recordings():
    """تنظيف التسجيلات القديمة"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح لك بصيانة النظام'}), 403
    
    days_old = request.json.get('days_old', 30)
    delete_files = request.json.get('delete_files', True)
    
    result = recording_manager.cleanup_old_recordings(days_old, delete_files)
    
    return jsonify({
        'success': True,
        'message': f'تم تنظيف {result["deleted_count"]} تسجيل',
        'deleted_count': result['deleted_count'],
        'freed_space_mb': round(result['freed_space'] / (1024 * 1024), 2)
    })

@bp.route('/maintenance/restart-services', methods=['POST'])
@login_required
def restart_services():
    """إعادة تشغيل خدمات النظام"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح لك بإعادة تشغيل الخدمات'}), 403
    
    try:
        # إعادة تشغيل مدير الكاميرات
        camera_manager.stop_monitoring()
        camera_manager.start_monitoring()
        
        # إعادة تشغيل مدير التسجيلات
        recording_manager.stop_service()
        recording_manager.start_service()
        
        return jsonify({
            'success': True,
            'message': 'تم إعادة تشغيل خدمات النظام بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إعادة تشغيل الخدمات: {str(e)}'
        })

@bp.route('/api/system-info')
@login_required
def api_system_info():
    """معلومات النظام API"""
    
    if not current_user.is_admin:
        return jsonify({'error': 'غير مسموح'}), 403
    
    try:
        import psutil
        import platform
        
        # معلومات النظام
        system_info = {
            'platform': platform.system(),
            'platform_release': platform.release(),
            'architecture': platform.machine(),
            'hostname': platform.node(),
            'python_version': platform.python_version(),
            'uptime': time.time() - psutil.boot_time()
        }
        
        # معلومات الذاكرة
        memory = psutil.virtual_memory()
        memory_info = {
            'total_gb': round(memory.total / (1024**3), 2),
            'available_gb': round(memory.available / (1024**3), 2),
            'used_gb': round(memory.used / (1024**3), 2),
            'percentage': memory.percent
        }
        
        # معلومات القرص
        disk = psutil.disk_usage('/')
        disk_info = {
            'total_gb': round(disk.total / (1024**3), 2),
            'used_gb': round(disk.used / (1024**3), 2),
            'free_gb': round(disk.free / (1024**3), 2),
            'percentage': round((disk.used / disk.total) * 100, 2)
        }
        
        # معلومات المعالج
        cpu_info = {
            'physical_cores': psutil.cpu_count(logical=False),
            'total_cores': psutil.cpu_count(logical=True),
            'usage_percentage': psutil.cpu_percent(interval=1)
        }
        
        return jsonify({
            'system': system_info,
            'memory': memory_info,
            'disk': disk_info,
            'cpu': cpu_info
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في الحصول على معلومات النظام: {str(e)}'}), 500
