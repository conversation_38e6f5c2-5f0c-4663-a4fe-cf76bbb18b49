# -*- coding: utf-8 -*-
"""
نماذج إدارة الكاميرات
Camera Management Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, SelectField, BooleanField, TextAreaField, PasswordField, SubmitField
from wtforms.validators import DataRequired, IPAddress, NumberRange, Length, Optional, URL
from wtforms.widgets import TextArea

class CameraForm(FlaskForm):
    """نموذج إضافة كاميرا جديدة"""
    
    # معلومات أساسية
    name = StringField('اسم الكاميرا', validators=[DataRequired(), Length(max=100)],
                      render_kw={'placeholder': 'أدخل اسم الكاميرا', 'class': 'form-control'})
    
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)],
                               render_kw={'placeholder': 'وصف اختياري للكاميرا', 'class': 'form-control', 'rows': 3})
    
    # معلومات الاتصال
    ip_address = StringField('عنوان IP', validators=[DataRequired(), IPAddress()],
                            render_kw={'placeholder': '*************', 'class': 'form-control'})
    
    port = IntegerField('المنفذ', validators=[DataRequired(), NumberRange(min=1, max=65535)],
                       default=554, render_kw={'class': 'form-control'})
    
    username = StringField('اسم المستخدم', validators=[Optional(), Length(max=50)],
                          render_kw={'placeholder': 'اسم المستخدم للكاميرا', 'class': 'form-control'})
    
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(max=100)],
                           render_kw={'placeholder': 'كلمة مرور الكاميرا', 'class': 'form-control'})
    
    # نوع الكاميرا والبروتوكول
    camera_type = SelectField('نوع الكاميرا', validators=[DataRequired()],
                             choices=[
                                 ('IP', 'كاميرا IP'),
                                 ('DVR', 'جهاز DVR'),
                                 ('NVR', 'جهاز NVR')
                             ],
                             render_kw={'class': 'form-select'})
    
    protocol = SelectField('البروتوكول', validators=[DataRequired()],
                          choices=[
                              ('RTSP', 'RTSP'),
                              ('HTTP', 'HTTP/MJPEG'),
                              ('ONVIF', 'ONVIF')
                          ],
                          default='RTSP',
                          render_kw={'class': 'form-select'})
    
    brand = StringField('الماركة', validators=[Optional(), Length(max=50)],
                       render_kw={'placeholder': 'Hikvision, Dahua, Uniview...', 'class': 'form-control'})
    
    model = StringField('الموديل', validators=[Optional(), Length(max=100)],
                       render_kw={'placeholder': 'موديل الكاميرا', 'class': 'form-control'})
    
    # رابط البث
    stream_url = StringField('رابط البث', validators=[Optional(), Length(max=500)],
                           render_kw={'placeholder': 'rtsp://*************:554/cam/realmonitor?channel=1&subtype=0', 'class': 'form-control'})
    
    # القنوات (للـ DVR/NVR)
    channel_number = IntegerField('رقم القناة', validators=[Optional(), NumberRange(min=1, max=64)],
                                 default=1, render_kw={'class': 'form-control'})
    
    total_channels = IntegerField('إجمالي القنوات', validators=[Optional(), NumberRange(min=1, max=64)],
                                 default=1, render_kw={'class': 'form-control'})
    
    # الموقع
    location = StringField('الموقع', validators=[Optional(), Length(max=200)],
                          render_kw={'placeholder': 'المدخل الرئيسي، الحديقة، المكتب...', 'class': 'form-control'})
    
    # الإعدادات
    is_active = BooleanField('تفعيل الكاميرا', default=True,
                           render_kw={'class': 'form-check-input'})
    
    recording_enabled = BooleanField('تفعيل التسجيل', default=True,
                                   render_kw={'class': 'form-check-input'})
    
    motion_detection = BooleanField('كشف الحركة', default=False,
                                  render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('إضافة الكاميرا', render_kw={'class': 'btn btn-primary'})
    
    def validate_stream_url(self, stream_url):
        """التحقق من صحة رابط البث"""
        if stream_url.data:
            protocol = self.protocol.data
            url = stream_url.data.lower()
            
            if protocol == 'RTSP' and not url.startswith('rtsp://'):
                raise ValidationError('رابط RTSP يجب أن يبدأ بـ rtsp://')
            elif protocol == 'HTTP' and not (url.startswith('http://') or url.startswith('https://')):
                raise ValidationError('رابط HTTP يجب أن يبدأ بـ http:// أو https://')

class CameraEditForm(CameraForm):
    """نموذج تعديل الكاميرا"""
    
    submit = SubmitField('حفظ التغييرات', render_kw={'class': 'btn btn-primary'})

class CameraSearchForm(FlaskForm):
    """نموذج البحث في الكاميرات"""
    
    search = StringField('البحث', validators=[Optional()],
                        render_kw={'placeholder': 'ابحث بالاسم أو الموقع أو IP...', 'class': 'form-control'})
    
    camera_type = SelectField('نوع الكاميرا', validators=[Optional()],
                             choices=[
                                 ('', 'جميع الأنواع'),
                                 ('IP', 'كاميرا IP'),
                                 ('DVR', 'جهاز DVR'),
                                 ('NVR', 'جهاز NVR')
                             ],
                             render_kw={'class': 'form-select'})
    
    status = SelectField('الحالة', validators=[Optional()],
                        choices=[
                            ('', 'جميع الحالات'),
                            ('online', 'متصل'),
                            ('offline', 'غير متصل'),
                            ('recording', 'يسجل')
                        ],
                        render_kw={'class': 'form-select'})
    
    submit = SubmitField('بحث', render_kw={'class': 'btn btn-outline-primary'})

class CameraSettingsForm(FlaskForm):
    """نموذج إعدادات الكاميرا المتقدمة"""
    
    # إعدادات التسجيل
    recording_quality = SelectField('جودة التسجيل', validators=[Optional()],
                                   choices=[
                                       ('low', 'منخفضة'),
                                       ('medium', 'متوسطة'),
                                       ('high', 'عالية')
                                   ],
                                   default='medium',
                                   render_kw={'class': 'form-select'})
    
    # إعدادات كشف الحركة
    motion_sensitivity = IntegerField('حساسية كشف الحركة', 
                                    validators=[Optional(), NumberRange(min=1, max=100)],
                                    default=50,
                                    render_kw={'class': 'form-range', 'min': '1', 'max': '100'})
    
    motion_threshold = IntegerField('عتبة كشف الحركة',
                                  validators=[Optional(), NumberRange(min=100, max=10000)],
                                  default=1000,
                                  render_kw={'class': 'form-control'})
    
    # جدولة التسجيل
    recording_schedule = TextAreaField('جدول التسجيل (JSON)',
                                     validators=[Optional()],
                                     render_kw={'class': 'form-control', 'rows': 5,
                                               'placeholder': '{"monday": {"start": "08:00", "end": "18:00"}, ...}'})
    
    # إعدادات التنبيهات
    alert_on_motion = BooleanField('تنبيه عند كشف الحركة', default=True,
                                 render_kw={'class': 'form-check-input'})
    
    alert_on_disconnect = BooleanField('تنبيه عند انقطاع الاتصال', default=True,
                                     render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('حفظ الإعدادات', render_kw={'class': 'btn btn-success'})

class BulkCameraForm(FlaskForm):
    """نموذج العمليات المجمعة على الكاميرات"""
    
    action = SelectField('الإجراء', validators=[DataRequired()],
                        choices=[
                            ('activate', 'تفعيل'),
                            ('deactivate', 'إلغاء تفعيل'),
                            ('start_recording', 'بدء التسجيل'),
                            ('stop_recording', 'إيقاف التسجيل'),
                            ('enable_motion', 'تفعيل كشف الحركة'),
                            ('disable_motion', 'إلغاء كشف الحركة'),
                            ('delete', 'حذف')
                        ],
                        render_kw={'class': 'form-select'})
    
    camera_ids = StringField('معرفات الكاميرات', validators=[DataRequired()],
                           render_kw={'type': 'hidden'})
    
    submit = SubmitField('تنفيذ', render_kw={'class': 'btn btn-warning'})

class CameraImportForm(FlaskForm):
    """نموذج استيراد الكاميرات من ملف"""
    
    import_format = SelectField('تنسيق الاستيراد', validators=[DataRequired()],
                               choices=[
                                   ('csv', 'ملف CSV'),
                                   ('json', 'ملف JSON'),
                                   ('xml', 'ملف XML')
                               ],
                               render_kw={'class': 'form-select'})
    
    file_content = TextAreaField('محتوى الملف', validators=[DataRequired()],
                               render_kw={'class': 'form-control', 'rows': 10,
                                         'placeholder': 'الصق محتوى الملف هنا...'})
    
    overwrite_existing = BooleanField('استبدال الكاميرات الموجودة', default=False,
                                    render_kw={'class': 'form-check-input'})
    
    submit = SubmitField('استيراد', render_kw={'class': 'btn btn-info'})

class ONVIFDiscoveryForm(FlaskForm):
    """نموذج اكتشاف كاميرات ONVIF"""
    
    network_range = StringField('نطاق الشبكة', validators=[DataRequired()],
                               default='192.168.1.0/24',
                               render_kw={'placeholder': '192.168.1.0/24', 'class': 'form-control'})
    
    timeout = IntegerField('مهلة الاكتشاف (ثانية)', validators=[Optional(), NumberRange(min=5, max=60)],
                          default=10,
                          render_kw={'class': 'form-control'})
    
    username = StringField('اسم المستخدم الافتراضي', validators=[Optional()],
                          render_kw={'placeholder': 'admin', 'class': 'form-control'})
    
    password = PasswordField('كلمة المرور الافتراضية', validators=[Optional()],
                           render_kw={'placeholder': 'admin', 'class': 'form-control'})
    
    submit = SubmitField('بدء الاكتشاف', render_kw={'class': 'btn btn-success'})
