# -*- coding: utf-8 -*-
"""
نماذج المصادقة
Authentication Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={'placeholder': 'أدخل اسم المستخدم', 'class': 'form-control'})
    password = PasswordField('كلمة المرور', validators=[DataRequired()],
                           render_kw={'placeholder': 'أدخل كلمة المرور', 'class': 'form-control'})
    remember_me = BooleanField('تذكرني', render_kw={'class': 'form-check-input'})
    submit = SubmitField('تسجيل الدخول', render_kw={'class': 'btn btn-primary btn-block'})

class RegisterForm(FlaskForm):
    """نموذج التسجيل"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)],
                          render_kw={'placeholder': 'اختر اسم المستخدم', 'class': 'form-control'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'class': 'form-control'})
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)],
                           render_kw={'placeholder': 'أدخل الاسم الكامل', 'class': 'form-control'})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={'placeholder': 'أدخل كلمة المرور', 'class': 'form-control'})
    password2 = PasswordField('تأكيد كلمة المرور', 
                            validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')],
                            render_kw={'placeholder': 'أعد إدخال كلمة المرور', 'class': 'form-control'})
    submit = SubmitField('إنشاء الحساب', render_kw={'class': 'btn btn-success btn-block'})
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم مستخدم بالفعل، يرجى اختيار اسم آخر')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل، يرجى استخدام بريد آخر')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()],
                                   render_kw={'placeholder': 'أدخل كلمة المرور الحالية', 'class': 'form-control'})
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)],
                               render_kw={'placeholder': 'أدخل كلمة المرور الجديدة', 'class': 'form-control'})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                                validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')],
                                render_kw={'placeholder': 'أعد إدخال كلمة المرور الجديدة', 'class': 'form-control'})
    submit = SubmitField('تغيير كلمة المرور', render_kw={'class': 'btn btn-primary'})

class EditProfileForm(FlaskForm):
    """نموذج تعديل الملف الشخصي"""
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)],
                           render_kw={'placeholder': 'أدخل الاسم الكامل', 'class': 'form-control'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'class': 'form-control'})
    phone = StringField('رقم الهاتف', validators=[Length(max=20)],
                       render_kw={'placeholder': 'أدخل رقم الهاتف', 'class': 'form-control'})
    submit = SubmitField('حفظ التغييرات', render_kw={'class': 'btn btn-primary'})
    
    def __init__(self, original_email, *args, **kwargs):
        super(EditProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('البريد الإلكتروني مستخدم بالفعل، يرجى استخدام بريد آخر')

class ResetPasswordRequestForm(FlaskForm):
    """نموذج طلب إعادة تعيين كلمة المرور"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'placeholder': 'أدخل البريد الإلكتروني', 'class': 'form-control'})
    submit = SubmitField('إرسال رابط إعادة التعيين', render_kw={'class': 'btn btn-primary btn-block'})

class ResetPasswordForm(FlaskForm):
    """نموذج إعادة تعيين كلمة المرور"""
    password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)],
                           render_kw={'placeholder': 'أدخل كلمة المرور الجديدة', 'class': 'form-control'})
    password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                            validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')],
                            render_kw={'placeholder': 'أعد إدخال كلمة المرور الجديدة', 'class': 'form-control'})
    submit = SubmitField('تعيين كلمة المرور', render_kw={'class': 'btn btn-primary btn-block'})
