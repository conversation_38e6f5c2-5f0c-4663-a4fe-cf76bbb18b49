# -*- coding: utf-8 -*-
"""
مسارات إدارة التسجيلات
Recording Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, desc, and_
import os

from app import db
from app.models import Recording, Camera
from app.utils.recording_manager import recording_manager

bp = Blueprint('recordings', __name__)

@bp.route('/')
@login_required
def index():
    """قائمة التسجيلات"""
    
    if not current_user.has_permission('can_view_recordings'):
        flash('غير مسموح لك بعرض التسجيلات', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # فلاتر البحث
    camera_id = request.args.get('camera_id', type=int)
    recording_type = request.args.get('type')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search')
    
    # بناء الاستعلام
    query = Recording.query
    
    # فلترة حسب الكاميرا
    if camera_id:
        query = query.filter_by(camera_id=camera_id)
    
    # فلترة حسب نوع التسجيل
    if recording_type:
        query = query.filter_by(recording_type=recording_type)
    
    # فلترة حسب التاريخ
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Recording.start_time >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(Recording.start_time < date_to_obj)
        except ValueError:
            pass
    
    # البحث النصي
    if search:
        query = query.join(Camera).filter(
            Camera.name.contains(search) |
            Recording.filename.contains(search)
        )
    
    # ترتيب وتقسيم الصفحات
    recordings_pagination = query.order_by(desc(Recording.start_time)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات التسجيلات
    stats = {
        'total': Recording.query.count(),
        'today': Recording.query.filter(
            Recording.start_time >= datetime.utcnow().date()
        ).count(),
        'this_week': Recording.query.filter(
            Recording.start_time >= datetime.utcnow().date() - timedelta(days=7)
        ).count(),
        'total_size': db.session.query(func.sum(Recording.file_size)).scalar() or 0,
        'by_type': dict(
            db.session.query(Recording.recording_type, func.count(Recording.id))
            .group_by(Recording.recording_type).all()
        )
    }
    
    # قائمة الكاميرات للفلتر
    cameras = Camera.query.filter_by(is_active=True).all()
    
    return render_template('recordings/index.html',
                         recordings=recordings_pagination.items,
                         pagination=recordings_pagination,
                         stats=stats,
                         cameras=cameras,
                         current_filters={
                             'camera_id': camera_id,
                             'type': recording_type,
                             'date_from': date_from,
                             'date_to': date_to,
                             'search': search
                         })

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل التسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if not current_user.has_permission('can_view_recordings'):
        flash('غير مسموح لك بعرض التسجيلات', 'error')
        return redirect(url_for('dashboard.index'))
    
    # التحقق من وجود الملف
    file_exists = recording.file_exists()
    
    return render_template('recordings/view.html',
                         recording=recording,
                         file_exists=file_exists)

@bp.route('/<int:id>/download')
@login_required
def download(id):
    """تحميل التسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if not current_user.has_permission('can_view_recordings'):
        return jsonify({'error': 'غير مسموح لك بتحميل التسجيلات'}), 403
    
    if not recording.file_exists():
        return jsonify({'error': 'ملف التسجيل غير موجود'}), 404
    
    return send_file(
        recording.get_full_path(),
        as_attachment=True,
        download_name=recording.filename
    )

@bp.route('/<int:id>/stream')
@login_required
def stream(id):
    """بث التسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if not current_user.has_permission('can_view_recordings'):
        return jsonify({'error': 'غير مسموح لك بمشاهدة التسجيلات'}), 403
    
    if not recording.file_exists():
        return jsonify({'error': 'ملف التسجيل غير موجود'}), 404
    
    # إرجاع ملف الفيديو للبث
    return send_file(
        recording.get_full_path(),
        mimetype='video/mp4'
    )

@bp.route('/<int:id>/thumbnail')
@login_required
def thumbnail(id):
    """الصورة المصغرة للتسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if recording.thumbnail_path and os.path.exists(recording.thumbnail_path):
        return send_file(recording.thumbnail_path, mimetype='image/jpeg')
    else:
        # إنشاء صورة مصغرة إذا لم تكن موجودة
        if recording.generate_thumbnail():
            return send_file(recording.thumbnail_path, mimetype='image/jpeg')
        else:
            # إرجاع صورة افتراضية
            return send_file('static/images/video-placeholder.jpg', mimetype='image/jpeg')

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف التسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if not current_user.has_permission('can_manage_recordings'):
        return jsonify({'error': 'غير مسموح لك بحذف التسجيلات'}), 403
    
    if recording.is_protected:
        return jsonify({'error': 'هذا التسجيل محمي من الحذف'}), 400
    
    # حذف الملف من القرص
    file_deleted = recording.delete_file()
    
    # حذف السجل من قاعدة البيانات
    recording_name = recording.filename
    db.session.delete(recording)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'تم حذف التسجيل {recording_name}',
        'file_deleted': file_deleted
    })

@bp.route('/<int:id>/protect', methods=['POST'])
@login_required
def protect(id):
    """حماية/إلغاء حماية التسجيل"""
    
    recording = Recording.query.get_or_404(id)
    
    if not current_user.has_permission('can_manage_recordings'):
        return jsonify({'error': 'غير مسموح لك بإدارة التسجيلات'}), 403
    
    recording.is_protected = not recording.is_protected
    db.session.commit()
    
    action = 'حماية' if recording.is_protected else 'إلغاء حماية'
    
    return jsonify({
        'success': True,
        'message': f'تم {action} التسجيل',
        'is_protected': recording.is_protected
    })

@bp.route('/search')
@login_required
def search():
    """البحث المتقدم في التسجيلات"""
    
    if not current_user.has_permission('can_view_recordings'):
        flash('غير مسموح لك بالبحث في التسجيلات', 'error')
        return redirect(url_for('dashboard.index'))
    
    cameras = Camera.query.filter_by(is_active=True).all()
    
    return render_template('recordings/search.html', cameras=cameras)

@bp.route('/schedule')
@login_required
def schedule():
    """جدولة التسجيل"""
    
    if not current_user.has_permission('can_manage_recordings'):
        flash('غير مسموح لك بإدارة جدولة التسجيل', 'error')
        return redirect(url_for('dashboard.index'))
    
    cameras = Camera.query.filter_by(is_active=True).all()
    
    return render_template('recordings/schedule.html', cameras=cameras)

@bp.route('/api/stats')
@login_required
def api_stats():
    """API لإحصائيات التسجيلات"""
    
    # إحصائيات يومية للأسبوع الماضي
    daily_stats = []
    for i in range(7):
        date = datetime.utcnow().date() - timedelta(days=i)
        count = Recording.query.filter(
            func.date(Recording.start_time) == date
        ).count()
        daily_stats.append({
            'date': date.isoformat(),
            'count': count
        })
    
    # إحصائيات حسب نوع التسجيل
    type_stats = dict(
        db.session.query(Recording.recording_type, func.count(Recording.id))
        .group_by(Recording.recording_type).all()
    )
    
    # إحصائيات حسب الكاميرا
    camera_stats = db.session.query(
        Camera.name,
        func.count(Recording.id).label('count'),
        func.sum(Recording.file_size).label('total_size')
    ).join(Recording).group_by(Camera.id, Camera.name).all()
    
    return jsonify({
        'daily_stats': daily_stats,
        'type_stats': type_stats,
        'camera_stats': [
            {
                'camera': stat[0],
                'count': stat[1],
                'total_size': stat[2] or 0
            }
            for stat in camera_stats
        ]
    })

@bp.route('/cleanup', methods=['POST'])
@login_required
def cleanup():
    """تنظيف التسجيلات القديمة"""
    
    if not current_user.has_permission('can_manage_recordings'):
        return jsonify({'error': 'غير مسموح لك بإدارة التسجيلات'}), 403
    
    days_old = request.json.get('days_old', 30)
    delete_files = request.json.get('delete_files', True)
    
    # البحث عن التسجيلات القديمة غير المحمية
    cutoff_date = datetime.utcnow() - timedelta(days=days_old)
    old_recordings = Recording.query.filter(
        and_(
            Recording.start_time < cutoff_date,
            Recording.is_protected == False
        )
    ).all()
    
    deleted_count = 0
    freed_space = 0
    
    for recording in old_recordings:
        if delete_files:
            if recording.delete_file():
                freed_space += recording.file_size or 0
        
        db.session.delete(recording)
        deleted_count += 1
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'تم حذف {deleted_count} تسجيل',
        'deleted_count': deleted_count,
        'freed_space_mb': round(freed_space / (1024 * 1024), 2)
    })

@bp.route('/export', methods=['POST'])
@login_required
def export():
    """تصدير قائمة التسجيلات"""
    
    if not current_user.has_permission('can_view_recordings'):
        return jsonify({'error': 'غير مسموح لك بعرض التسجيلات'}), 403
    
    export_format = request.json.get('format', 'csv')
    
    # الحصول على التسجيلات مع الفلاتر المطبقة
    recordings = Recording.query.join(Camera).all()
    
    if export_format == 'csv':
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # كتابة الرأس
        writer.writerow([
            'اسم الملف', 'الكاميرا', 'نوع التسجيل', 'تاريخ البداية',
            'تاريخ النهاية', 'المدة', 'حجم الملف', 'الجودة'
        ])
        
        # كتابة البيانات
        for recording in recordings:
            writer.writerow([
                recording.filename,
                recording.camera.name,
                recording.recording_type,
                recording.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                recording.end_time.strftime('%Y-%m-%d %H:%M:%S') if recording.end_time else '',
                recording.get_duration_formatted(),
                recording.get_file_size_mb(),
                recording.quality
            ])
        
        output.seek(0)
        
        return jsonify({
            'success': True,
            'data': output.getvalue(),
            'filename': f'recordings_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        })
    
    return jsonify({'error': 'تنسيق التصدير غير مدعوم'}), 400
