#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات الاحترافي
Professional Surveillance Camera System
Entry point for the application
"""

import os
import sys
import signal
import threading
import time
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    from app.models import User, Camera, Recording, Alert
    from app.utils.camera_manager import camera_manager
    from app.utils.recording_manager import recording_manager
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    sys.exit(1)

def deploy():
    """تهيئة النظام وقاعدة البيانات"""
    try:
        # إنشاء جداول قاعدة البيانات
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات")

        # إنشاء المستخدم الافتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                is_admin=True,
                is_active=True,
                can_view_cameras=True,
                can_control_cameras=True,
                can_manage_recordings=True,
                can_manage_users=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("👤 تم إنشاء المستخدم الافتراضي: admin/admin123")
        else:
            print("👤 المستخدم الافتراضي موجود")

        # إنشاء المجلدات المطلوبة
        from config.config import Config
        os.makedirs(Config.RECORDINGS_FOLDER, exist_ok=True)
        os.makedirs(Config.SNAPSHOTS_FOLDER, exist_ok=True)
        print("📁 تم إنشاء مجلدات النظام")

        return True

    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام: {e}")
        return False

def start_background_services():
    """بدء الخدمات الخلفية"""
    try:
        # بدء مدير الكاميرات
        camera_manager.start_monitoring()
        print("📹 تم بدء خدمة مراقبة الكاميرات")

        # بدء مدير التسجيلات
        recording_manager.start_service()
        print("🎥 تم بدء خدمة التسجيل")

        return True

    except Exception as e:
        print(f"❌ خطأ في بدء الخدمات الخلفية: {e}")
        return False

def stop_background_services():
    """إيقاف الخدمات الخلفية"""
    try:
        print("\n🛑 إيقاف الخدمات الخلفية...")

        # إيقاف مدير التسجيلات
        recording_manager.stop_service()
        print("✅ تم إيقاف خدمة التسجيل")

        # إيقاف مدير الكاميرات
        camera_manager.stop_monitoring()
        print("✅ تم إيقاف خدمة مراقبة الكاميرات")

    except Exception as e:
        print(f"❌ خطأ في إيقاف الخدمات: {e}")

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    print(f"\n📡 تم استلام إشارة {signum}")
    stop_background_services()
    print("👋 تم إيقاف النظام بأمان")
    sys.exit(0)

# إنشاء التطبيق
app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'User': User,
        'Camera': Camera,
        'Recording': Recording,
        'Alert': Alert,
        'camera_manager': camera_manager,
        'recording_manager': recording_manager
    }

if __name__ == '__main__':
    # تسجيل معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("🚀 بدء تشغيل نظام مراقبة الكاميرات الاحترافي")
    print("=" * 60)

    with app.app_context():
        # تهيئة النظام
        if not deploy():
            print("❌ فشل في تهيئة النظام")
            sys.exit(1)

        # بدء الخدمات الخلفية
        if not start_background_services():
            print("❌ فشل في بدء الخدمات الخلفية")
            sys.exit(1)

    print("=" * 60)
    print("🎉 تم تشغيل النظام بنجاح!")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)

    try:
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # تعطيل وضع التطوير للإنتاج
            threaded=True,
            use_reloader=False  # تعطيل إعادة التحميل التلقائي
        )
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        stop_background_services()
        sys.exit(1)
