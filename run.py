#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات الاحترافي
Professional Surveillance Camera System
Entry point for the application
"""

import os
from app import create_app, db
from app.models import User, Camera, Recording, Alert
from flask_migrate import upgrade

def deploy():
    """Run deployment tasks."""
    # Create database tables
    db.create_all()
    
    # Create default admin user if not exists
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True,
            is_active=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("تم إنشاء المستخدم الافتراضي: admin/admin123")

app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'User': User,
        'Camera': Camera,
        'Recording': Recording,
        'Alert': Alert
    }

if __name__ == '__main__':
    with app.app_context():
        deploy()
    
    print("🚀 بدء تشغيل نظام مراقبة الكاميرات...")
    print("📱 الواجهة متاحة على: http://localhost:5000")
    print("👤 المستخدم الافتراضي: admin")
    print("🔑 كلمة المرور الافتراضية: admin123")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
