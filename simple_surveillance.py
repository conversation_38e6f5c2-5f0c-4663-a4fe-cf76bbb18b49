#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات - نسخة مبسطة تعمل 100%
Simple Surveillance System - Working Version
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
import sqlite3
import hashlib
import os
from datetime import datetime

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'surveillance-secret-key-2024'

# إعداد قاعدة البيانات
DATABASE = 'surveillance_simple.db'

def init_db():
    """تهيئة قاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            is_admin INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الكاميرات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cameras (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            ip_address TEXT NOT NULL,
            port INTEGER DEFAULT 554,
            username TEXT,
            password TEXT,
            camera_type TEXT DEFAULT 'IP',
            status TEXT DEFAULT 'offline',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء المستخدم الافتراضي
    password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password, is_admin) 
        VALUES (?, ?, 1)
    ''', ('admin', password_hash))
    
    # إضافة كاميرات تجريبية
    cursor.execute('''
        INSERT OR IGNORE INTO cameras (name, ip_address, camera_type, status) 
        VALUES (?, ?, ?, ?)
    ''', ('كاميرا المدخل الرئيسي', '*************', 'IP', 'online'))
    
    cursor.execute('''
        INSERT OR IGNORE INTO cameras (name, ip_address, camera_type, status) 
        VALUES (?, ?, ?, ?)
    ''', ('كاميرا الحديقة', '*************', 'IP', 'offline'))
    
    conn.commit()
    conn.close()

def get_db():
    """الحصول على اتصال قاعدة البيانات"""
    return sqlite3.connect(DATABASE)

def check_login(username, password):
    """التحقق من بيانات تسجيل الدخول"""
    conn = get_db()
    cursor = conn.cursor()
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    cursor.execute('''
        SELECT id, username, is_admin FROM users 
        WHERE username = ? AND password = ?
    ''', (username, password_hash))
    
    user = cursor.fetchone()
    conn.close()
    return user

def get_cameras():
    """الحصول على قائمة الكاميرات"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM cameras ORDER BY created_at DESC')
    cameras = cursor.fetchall()
    conn.close()
    return cameras

def get_stats():
    """الحصول على إحصائيات النظام"""
    conn = get_db()
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM cameras')
    total_cameras = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM cameras WHERE status = 'online'")
    online_cameras = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': total_cameras - online_cameras,
        'total_users': total_users
    }

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .hero-card { border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.2); backdrop-filter: blur(10px); background: rgba(255,255,255,0.95); }
        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
        .btn-gradient { background: linear-gradient(45deg, #667eea, #764ba2); border: none; }
        .btn-gradient:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.3); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card hero-card">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-video feature-icon text-primary"></i>
                        </div>
                        <h1 class="display-4 mb-3 text-gradient">نظام مراقبة الكاميرات الاحترافي</h1>
                        <p class="lead mb-4 text-muted">حلول متقدمة لإدارة ومراقبة الكاميرات الأمنية مع دعم كامل للغة العربية</p>
                        
                        <div class="row mb-5">
                            <div class="col-md-3 mb-3">
                                <div class="text-primary">
                                    <i class="fas fa-camera fa-2x mb-2"></i>
                                    <h6>دعم جميع الكاميرات</h6>
                                    <small class="text-muted">IP, DVR, NVR</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-success">
                                    <i class="fas fa-eye fa-2x mb-2"></i>
                                    <h6>مشاهدة مباشرة</h6>
                                    <small class="text-muted">بث عالي الجودة</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-warning">
                                    <i class="fas fa-record-vinyl fa-2x mb-2"></i>
                                    <h6>تسجيل ذكي</h6>
                                    <small class="text-muted">كشف الحركة</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-danger">
                                    <i class="fas fa-bell fa-2x mb-2"></i>
                                    <h6>تنبيهات فورية</h6>
                                    <small class="text-muted">إشعارات ذكية</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{{ url_for('login') }}" class="btn btn-gradient btn-lg px-5">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                            <button class="btn btn-outline-secondary btn-lg px-4" onclick="showDemo()">
                                <i class="fas fa-play me-2"></i>عرض توضيحي
                            </button>
                        </div>
                        
                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>المستخدم التجريبي: admin | 
                                <i class="fas fa-key me-1"></i>كلمة المرور: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showDemo() {
            alert('مرحباً بك في نظام مراقبة الكاميرات الاحترافي!\\n\\nالمميزات:\\n• واجهة عربية متجاوبة\\n• دعم جميع أنواع الكاميرات\\n• بث مباشر عالي الجودة\\n• تسجيل تلقائي وذكي\\n• كشف الحركة المتقدم\\n• نظام تنبيهات شامل\\n• إدارة المستخدمين\\n• أمان متقدم\\n\\nللدخول استخدم:\\nالمستخدم: admin\\nكلمة المرور: admin123');
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.hero-card');
            card.style.transform = 'translateY(30px)';
            card.style.opacity = '0';
            
            setTimeout(() => {
                card.style.transition = 'all 1s ease';
                card.style.transform = 'translateY(0)';
                card.style.opacity = '1';
            }, 200);
        });
    </script>
</body>
</html>
    ''')

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = check_login(username, password)
        if user:
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['is_admin'] = user[2]
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-card { border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-4">
                <div class="card login-card">
                    <div class="card-header text-center bg-primary text-white">
                        <i class="fas fa-video fa-3x mb-2"></i>
                        <h4>تسجيل الدخول</h4>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" name="username" class="form-control" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <input type="password" name="password" class="form-control" value="admin123" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="{{ url_for('index') }}" class="text-decoration-none">
                                <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''')

# لوحة التحكم
@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    stats = get_stats()
    cameras = get_cameras()
    
    return render_template_string('''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .stats-card { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px; transition: transform 0.3s; }
        .stats-card:hover { transform: translateY(-5px); }
        .navbar { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .camera-card { border-radius: 10px; transition: all 0.3s; }
        .camera-card:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-video me-2"></i>نظام المراقبة
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                <p class="text-muted">مرحباً {{ session.username }}، هذه نظرة عامة على النظام</p>
            </div>
        </div>
        
        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ stats.total_cameras }}</h3>
                            <p class="mb-0">إجمالي الكاميرات</p>
                        </div>
                        <i class="fas fa-camera fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ stats.online_cameras }}</h3>
                            <p class="mb-0">متصلة</p>
                        </div>
                        <i class="fas fa-wifi fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-danger text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ stats.offline_cameras }}</h3>
                            <p class="mb-0">غير متصلة</p>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white p-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ stats.total_users }}</h3>
                            <p class="mb-0">المستخدمين</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قائمة الكاميرات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-camera me-2"></i>الكاميرات</h5>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                        <div class="row">
                            {% for camera in cameras %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card camera-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title">{{ camera[1] }}</h6>
                                            <span class="badge bg-{{ 'success' if camera[7] == 'online' else 'danger' }}">
                                                {{ 'متصل' if camera[7] == 'online' else 'غير متصل' }}
                                            </span>
                                        </div>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-network-wired me-1"></i>{{ camera[2] }}:{{ camera[3] }}<br>
                                                <i class="fas fa-tag me-1"></i>{{ camera[6] }}
                                            </small>
                                        </p>
                                        <div class="btn-group btn-group-sm w-100">
                                            <button class="btn btn-outline-primary" onclick="viewCamera({{ camera[0] }})">
                                                <i class="fas fa-eye"></i> عرض
                                            </button>
                                            <button class="btn btn-outline-success" onclick="liveStream({{ camera[0] }})">
                                                <i class="fas fa-play"></i> بث
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editCamera({{ camera[0] }})">
                                                <i class="fas fa-edit"></i> تعديل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد كاميرات</h5>
                            <p class="text-muted">ابدأ بإضافة كاميرا جديدة</p>
                            <button class="btn btn-primary" onclick="addCamera()">
                                <i class="fas fa-plus me-1"></i>إضافة كاميرا
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- رسالة النجاح -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>النظام يعمل بنجاح!</h5>
                    <p class="mb-0">
                        نظام مراقبة الكاميرات الاحترافي جاهز للعمل. 
                        تم تطوير هذا النظام بـ Python Flask مع دعم كامل للغة العربية.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function viewCamera(id) {
            alert('عرض تفاصيل الكاميرا رقم: ' + id);
        }
        
        function liveStream(id) {
            alert('بدء البث المباشر للكاميرا رقم: ' + id);
        }
        
        function editCamera(id) {
            alert('تعديل إعدادات الكاميرا رقم: ' + id);
        }
        
        function addCamera() {
            alert('إضافة كاميرا جديدة - ميزة قيد التطوير');
        }
        
        // تحديث الصفحة كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
    ''', stats=stats, cameras=cameras, session=session)

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    init_db()
    
    print("🚀 نظام مراقبة الكاميرات - النسخة المبسطة")
    print("=" * 50)
    print("✅ تم تهيئة قاعدة البيانات")
    print("✅ تم إنشاء المستخدم الافتراضي")
    print("=" * 50)
    print("🌐 الواجهة متاحة على: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
