# -*- coding: utf-8 -*-
"""
نموذج التسجيل
Recording Model
"""

from datetime import datetime
import os
from app import db

class Recording(db.Model):
    """نموذج التسجيل"""
    
    __tablename__ = 'recordings'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255))
    
    # معلومات التسجيل
    duration = db.Column(db.Integer)  # بالثواني
    file_size = db.Column(db.BigInteger)  # بالبايت
    file_path = db.Column(db.String(500), nullable=False)
    thumbnail_path = db.Column(db.String(500))
    
    # نوع التسجيل
    recording_type = db.Column(db.String(20), default='manual')  # manual, scheduled, motion, alert
    quality = db.Column(db.String(20), default='medium')  # low, medium, high
    format = db.Column(db.String(10), default='mp4')  # mp4, avi, mkv
    
    # معلومات الفيديو
    resolution = db.Column(db.String(20))  # 1920x1080, 1280x720, etc.
    fps = db.Column(db.Integer, default=25)
    bitrate = db.Column(db.Integer)  # kbps
    codec = db.Column(db.String(20), default='h264')
    
    # التواريخ
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # الحالة
    status = db.Column(db.String(20), default='recording')  # recording, completed, failed, deleted
    is_archived = db.Column(db.Boolean, default=False)
    is_protected = db.Column(db.Boolean, default=False)  # محمي من الحذف
    
    # العلاقات الخارجية
    camera_id = db.Column(db.Integer, db.ForeignKey('cameras.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # الفهارس
    __table_args__ = (
        db.Index('idx_recording_camera_date', 'camera_id', 'start_time'),
        db.Index('idx_recording_type_date', 'recording_type', 'start_time'),
        db.Index('idx_recording_status', 'status'),
    )
    
    def get_file_size_mb(self):
        """الحصول على حجم الملف بالميجابايت"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
    
    def get_duration_formatted(self):
        """الحصول على مدة التسجيل منسقة"""
        if not self.duration:
            return "00:00:00"
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def file_exists(self):
        """التحقق من وجود الملف"""
        return os.path.exists(self.file_path)
    
    def get_full_path(self):
        """الحصول على المسار الكامل للملف"""
        return os.path.abspath(self.file_path)
    
    def delete_file(self):
        """حذف ملف التسجيل"""
        try:
            if os.path.exists(self.file_path):
                os.remove(self.file_path)
            
            # حذف الصورة المصغرة إن وجدت
            if self.thumbnail_path and os.path.exists(self.thumbnail_path):
                os.remove(self.thumbnail_path)
            
            return True
        except Exception as e:
            print(f"خطأ في حذف الملف: {e}")
            return False
    
    def generate_thumbnail(self):
        """إنشاء صورة مصغرة للتسجيل"""
        try:
            import cv2
            import os
            from config.config import Config
            
            if not self.file_exists():
                return False
            
            # إنشاء مجلد الصور المصغرة
            thumbnails_dir = os.path.join(Config.SNAPSHOTS_FOLDER, 'thumbnails')
            os.makedirs(thumbnails_dir, exist_ok=True)
            
            # اسم الصورة المصغرة
            thumbnail_name = f"thumb_{self.id}_{self.filename.rsplit('.', 1)[0]}.jpg"
            thumbnail_path = os.path.join(thumbnails_dir, thumbnail_name)
            
            # استخراج إطار من الفيديو
            cap = cv2.VideoCapture(self.file_path)
            
            # الانتقال إلى منتصف الفيديو
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            middle_frame = total_frames // 2
            cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
            
            ret, frame = cap.read()
            if ret:
                # تغيير حجم الصورة
                height, width = frame.shape[:2]
                new_width = 320
                new_height = int((new_width * height) / width)
                
                resized_frame = cv2.resize(frame, (new_width, new_height))
                
                # حفظ الصورة المصغرة
                cv2.imwrite(thumbnail_path, resized_frame)
                
                self.thumbnail_path = thumbnail_path
                db.session.commit()
                
                cap.release()
                return True
            
            cap.release()
            return False
            
        except Exception as e:
            print(f"خطأ في إنشاء الصورة المصغرة: {e}")
            return False
    
    def update_file_info(self):
        """تحديث معلومات الملف"""
        try:
            if self.file_exists():
                # حجم الملف
                self.file_size = os.path.getsize(self.file_path)
                
                # معلومات الفيديو باستخدام OpenCV
                import cv2
                cap = cv2.VideoCapture(self.file_path)
                
                if cap.isOpened():
                    # عدد الإطارات ومعدل الإطارات
                    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    
                    if fps > 0:
                        self.duration = int(total_frames / fps)
                        self.fps = int(fps)
                    
                    # الدقة
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    self.resolution = f"{width}x{height}"
                
                cap.release()
                db.session.commit()
                
        except Exception as e:
            print(f"خطأ في تحديث معلومات الملف: {e}")
    
    def __repr__(self):
        return f'<Recording {self.filename} - Camera {self.camera_id}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'filename': self.filename,
            'duration': self.duration,
            'duration_formatted': self.get_duration_formatted(),
            'file_size': self.file_size,
            'file_size_mb': self.get_file_size_mb(),
            'recording_type': self.recording_type,
            'quality': self.quality,
            'format': self.format,
            'resolution': self.resolution,
            'fps': self.fps,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'is_archived': self.is_archived,
            'is_protected': self.is_protected,
            'camera_id': self.camera_id,
            'camera_name': self.camera.name if self.camera else None,
            'created_at': self.created_at.isoformat()
        }
