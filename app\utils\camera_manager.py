# -*- coding: utf-8 -*-
"""
مدير الكاميرات
Camera Manager
"""

import cv2
import threading
import time
import requests
from datetime import datetime
import logging
from typing import Optional, Dict, Any

from app import db
from app.models import Camera, Alert
from config.config import Config

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CameraManager:
    """مدير الكاميرات والاتصالات"""
    
    def __init__(self):
        self.active_connections = {}
        self.connection_threads = {}
        self.is_running = False
        
    def start_monitoring(self):
        """بدء مراقبة الكاميرات"""
        self.is_running = True
        monitor_thread = threading.Thread(target=self._monitor_cameras)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("تم بدء مراقبة الكاميرات")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الكاميرات"""
        self.is_running = False
        
        # إغلاق جميع الاتصالات
        for camera_id in list(self.active_connections.keys()):
            self.disconnect_camera(camera_id)
        
        logger.info("تم إيقاف مراقبة الكاميرات")
    
    def _monitor_cameras(self):
        """مراقبة دورية للكاميرات"""
        while self.is_running:
            try:
                cameras = Camera.query.filter_by(is_active=True).all()
                
                for camera in cameras:
                    if camera.id not in self.active_connections:
                        self._attempt_connection(camera)
                    else:
                        self._check_connection_health(camera)
                
                time.sleep(30)  # فحص كل 30 ثانية
                
            except Exception as e:
                logger.error(f"خطأ في مراقبة الكاميرات: {e}")
                time.sleep(60)
    
    def _attempt_connection(self, camera: Camera):
        """محاولة الاتصال بالكاميرا"""
        try:
            if camera.protocol == 'RTSP':
                success = self._connect_rtsp(camera)
            elif camera.protocol == 'HTTP':
                success = self._connect_http(camera)
            elif camera.protocol == 'ONVIF':
                success = self._connect_onvif(camera)
            else:
                logger.warning(f"بروتوكول غير مدعوم: {camera.protocol}")
                return False
            
            if success:
                camera.update_connection_status('connected')
                logger.info(f"تم الاتصال بالكاميرا: {camera.name}")
                
                # إنشاء تنبيه استعادة الاتصال إذا كانت منقطعة سابقاً
                if camera.connection_attempts > 0:
                    Alert.create_connection_alert(camera, is_connected=True)
            else:
                camera.update_connection_status('disconnected')
                logger.warning(f"فشل الاتصال بالكاميرا: {camera.name}")
                
                # إنشاء تنبيه انقطاع الاتصال
                if camera.connection_attempts >= Config.CAMERA_RETRY_ATTEMPTS:
                    Alert.create_connection_alert(camera, is_connected=False)
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في الاتصال بالكاميرا {camera.name}: {e}")
            camera.update_connection_status('error')
            return False
    
    def _connect_rtsp(self, camera: Camera) -> bool:
        """الاتصال عبر RTSP"""
        try:
            stream_url = camera.get_stream_url()
            if not stream_url:
                return False
            
            cap = cv2.VideoCapture(stream_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_TIMEOUT, Config.CAMERA_CONNECTION_TIMEOUT * 1000)
            
            # اختبار قراءة إطار واحد
            ret, frame = cap.read()
            
            if ret and frame is not None:
                self.active_connections[camera.id] = {
                    'type': 'rtsp',
                    'capture': cap,
                    'last_frame': frame,
                    'last_update': datetime.utcnow()
                }
                return True
            else:
                cap.release()
                return False
                
        except Exception as e:
            logger.error(f"خطأ RTSP للكاميرا {camera.name}: {e}")
            return False
    
    def _connect_http(self, camera: Camera) -> bool:
        """الاتصال عبر HTTP"""
        try:
            stream_url = camera.get_stream_url()
            if not stream_url:
                return False
            
            # اختبار الاتصال HTTP
            response = requests.get(
                stream_url,
                timeout=Config.CAMERA_CONNECTION_TIMEOUT,
                auth=(camera.username, camera.password) if camera.username else None
            )
            
            if response.status_code == 200:
                self.active_connections[camera.id] = {
                    'type': 'http',
                    'url': stream_url,
                    'last_update': datetime.utcnow()
                }
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"خطأ HTTP للكاميرا {camera.name}: {e}")
            return False
    
    def _connect_onvif(self, camera: Camera) -> bool:
        """الاتصال عبر ONVIF"""
        try:
            from onvif import ONVIFCamera
            
            onvif_cam = ONVIFCamera(
                camera.ip_address,
                camera.port,
                camera.username,
                camera.password
            )
            
            # اختبار الاتصال
            device_info = onvif_cam.devicemgmt.GetDeviceInformation()
            
            if device_info:
                # الحصول على رابط البث
                media_service = onvif_cam.create_media_service()
                profiles = media_service.GetProfiles()
                
                if profiles:
                    profile = profiles[0]
                    stream_uri = media_service.GetStreamUri({
                        'StreamSetup': {
                            'Stream': 'RTP-Unicast',
                            'Transport': {'Protocol': 'RTSP'}
                        },
                        'ProfileToken': profile.token
                    })
                    
                    # حفظ رابط البث
                    camera.rtsp_url = stream_uri.Uri
                    db.session.commit()
                    
                    # محاولة الاتصال عبر RTSP
                    return self._connect_rtsp(camera)
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ ONVIF للكاميرا {camera.name}: {e}")
            return False
    
    def _check_connection_health(self, camera: Camera):
        """فحص صحة الاتصال"""
        try:
            connection = self.active_connections.get(camera.id)
            if not connection:
                return
            
            # فحص آخر تحديث
            time_diff = (datetime.utcnow() - connection['last_update']).seconds
            
            if time_diff > 60:  # إذا لم يتم التحديث لأكثر من دقيقة
                logger.warning(f"اتصال الكاميرا {camera.name} قديم، إعادة الاتصال...")
                self.disconnect_camera(camera.id)
                self._attempt_connection(camera)
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة الاتصال للكاميرا {camera.name}: {e}")
    
    def disconnect_camera(self, camera_id: int):
        """قطع الاتصال مع الكاميرا"""
        try:
            connection = self.active_connections.get(camera_id)
            if connection:
                if connection['type'] == 'rtsp' and 'capture' in connection:
                    connection['capture'].release()
                
                del self.active_connections[camera_id]
                
                # تحديث حالة الكاميرا
                camera = Camera.query.get(camera_id)
                if camera:
                    camera.update_connection_status('disconnected')
                
                logger.info(f"تم قطع الاتصال مع الكاميرا ID: {camera_id}")
            
        except Exception as e:
            logger.error(f"خطأ في قطع الاتصال مع الكاميرا {camera_id}: {e}")
    
    def get_frame(self, camera_id: int) -> Optional[bytes]:
        """الحصول على إطار من الكاميرا"""
        try:
            connection = self.active_connections.get(camera_id)
            if not connection:
                return None
            
            if connection['type'] == 'rtsp':
                cap = connection['capture']
                ret, frame = cap.read()
                
                if ret and frame is not None:
                    # تحديث آخر إطار ووقت التحديث
                    connection['last_frame'] = frame
                    connection['last_update'] = datetime.utcnow()
                    
                    # تحويل الإطار إلى JPEG
                    ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                    if ret:
                        return buffer.tobytes()
                
                return None
            
            elif connection['type'] == 'http':
                # للكاميرات HTTP، نحتاج لتنفيذ منطق مختلف
                camera = Camera.query.get(camera_id)
                if camera:
                    response = requests.get(
                        connection['url'],
                        timeout=5,
                        auth=(camera.username, camera.password) if camera.username else None
                    )
                    
                    if response.status_code == 200:
                        connection['last_update'] = datetime.utcnow()
                        return response.content
                
                return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إطار من الكاميرا {camera_id}: {e}")
            return None
    
    def is_camera_connected(self, camera_id: int) -> bool:
        """التحقق من اتصال الكاميرا"""
        return camera_id in self.active_connections
    
    def get_connection_info(self, camera_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الاتصال"""
        connection = self.active_connections.get(camera_id)
        if connection:
            return {
                'type': connection['type'],
                'last_update': connection['last_update'],
                'connected': True
            }
        return None

# إنشاء مثيل عام من مدير الكاميرات
camera_manager = CameraManager()
