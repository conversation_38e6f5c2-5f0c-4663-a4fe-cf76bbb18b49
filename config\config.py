# -*- coding: utf-8 -*-
"""
إعدادات النظام
System Configuration
"""

import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    """إعدادات النظام الأساسية"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'surveillance-system-secret-key-2024'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, '..', 'surveillance.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج
    SESSION_COOKIE_HTTPONLY = True
    
    # إعدادات اللغة
    LANGUAGES = ['ar', 'en']
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Qatar'
    
    # إعدادات التسجيل
    RECORDINGS_FOLDER = os.path.join(basedir, '..', 'recordings')
    SNAPSHOTS_FOLDER = os.path.join(basedir, '..', 'snapshots')
    MAX_RECORDING_SIZE = 1024 * 1024 * 1024  # 1GB
    
    # إعدادات البث المباشر
    STREAM_QUALITY = 'medium'  # low, medium, high
    MAX_CONCURRENT_STREAMS = 10
    STREAM_TIMEOUT = 30  # ثانية
    
    # إعدادات كشف الحركة
    MOTION_DETECTION_ENABLED = True
    MOTION_SENSITIVITY = 50  # 1-100
    MOTION_THRESHOLD = 1000
    
    # إعدادات التنبيهات
    SMTP_SERVER = os.environ.get('SMTP_SERVER') or 'smtp.gmail.com'
    SMTP_PORT = int(os.environ.get('SMTP_PORT') or 587)
    SMTP_USERNAME = os.environ.get('SMTP_USERNAME')
    SMTP_PASSWORD = os.environ.get('SMTP_PASSWORD')
    
    # إعدادات الكاميرات
    CAMERA_CONNECTION_TIMEOUT = 10  # ثانية
    CAMERA_RETRY_ATTEMPTS = 3
    SUPPORTED_PROTOCOLS = ['RTSP', 'HTTP', 'ONVIF']
    
    # إعدادات الأمان
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 30  # دقيقة
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        # إنشاء المجلدات المطلوبة
        os.makedirs(Config.RECORDINGS_FOLDER, exist_ok=True)
        os.makedirs(Config.SNAPSHOTS_FOLDER, exist_ok=True)

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    
class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
