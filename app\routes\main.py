# -*- coding: utf-8 -*-
"""
المسارات الرئيسية
Main Routes
"""

from flask import Blueprint, render_template, redirect, url_for, session, request
from flask_login import current_user

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    return render_template('main/index.html')

@bp.route('/language/<language>')
def set_language(language):
    """تغيير اللغة"""
    if language in ['ar', 'en']:
        session['language'] = language
    
    # العودة إلى الصفحة السابقة أو الصفحة الرئيسية
    return redirect(request.referrer or url_for('main.index'))

@bp.route('/about')
def about():
    """صفحة حول النظام"""
    return render_template('main/about.html')

@bp.route('/contact')
def contact():
    """صفحة الاتصال"""
    return render_template('main/contact.html')

@bp.route('/help')
def help():
    """صفحة المساعدة"""
    return render_template('main/help.html')

@bp.route('/privacy')
def privacy():
    """صفحة سياسة الخصوصية"""
    return render_template('main/privacy.html')

@bp.route('/terms')
def terms():
    """صفحة شروط الاستخدام"""
    return render_template('main/terms.html')
