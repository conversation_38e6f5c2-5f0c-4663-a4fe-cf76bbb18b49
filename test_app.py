#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
Simple System Test
"""

import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 اختبار استيراد الوحدات...")
    
    # اختبار استيراد Flask
    import flask
    print(f"✅ Flask {flask.__version__}")
    
    # اختبار استيراد SQLAlchemy
    import sqlalchemy
    print(f"✅ SQLAlchemy {sqlalchemy.__version__}")
    
    # اختبار استيراد OpenCV
    import cv2
    print(f"✅ OpenCV {cv2.__version__}")
    
    # اختبار إنشاء التطبيق
    print("\n🚀 اختبار إنشاء التطبيق...")
    from app import create_app, db
    
    app = create_app()
    print("✅ تم إنشاء التطبيق بنجاح")
    
    # اختبار قاعدة البيانات
    print("\n💾 اختبار قاعدة البيانات...")
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # اختبار النماذج
        from app.models import User, Camera, Recording, Alert
        print("✅ تم تحميل النماذج")
        
        # إنشاء مستخدم تجريبي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                is_active=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي")
        else:
            print("✅ المستخدم الافتراضي موجود")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("📱 يمكنك الآن تشغيل النظام باستخدام: python run.py")
    print("🌐 ثم انتقل إلى: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
